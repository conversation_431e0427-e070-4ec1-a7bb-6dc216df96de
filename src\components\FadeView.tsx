import React, { useEffect, useRef } from 'react';
import { Animated, ViewProps } from 'react-native';

interface FadeViewProps extends ViewProps {
  visible: boolean;
  duration?: number;
  children: React.ReactNode;
}

/**
 * A component that fades its children in and out
 */
const FadeView: React.FC<FadeViewProps> = ({
  visible,
  duration = 300,
  children,
  style,
  ...props
}) => {
  const opacity = useRef(new Animated.Value(visible ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: visible ? 1 : 0,
      duration,
      useNativeDriver: true,
    }).start();
  }, [visible, opacity, duration]);

  return (
    <Animated.View
      style={[
        {
          opacity,
        },
        style,
      ]}
      {...props}>
      {children}
    </Animated.View>
  );
};

export default FadeView;
