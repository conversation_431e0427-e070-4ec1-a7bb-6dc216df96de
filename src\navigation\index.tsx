/**
 * @fileoverview Main Navigation - Root navigation configuration for the Kiosk application
 * @module Navigation/Index
 * @category Navigation
 */

import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {
  StatusBar,
  View,
  ImageRequireSource,
  ImageSourcePropType,
} from 'react-native';
import KioskConfig from '../config/KioskConfig';
import SplashScreen from '../screens/SplashScreen';
import WelcomeScreen from '../screens/WelcomeScreen';
import HomeScreen from '../screens/HomeScreen';
import RentDemoBuyScreen from '../screens/RentDemoBuyScreen';
import LanguageScreen from '../screens/LanguageScreen';
import FaqScreen from '../screens/FaqScreen';
import SelectSport from '../screens/SelectSport';
import AppDemo from '../screens/AppDemo';
import ReservedOrReturnScreen from '../screens/ReservedOrReturnScreen/ReservedOrReturnScreen';
import OTPorQRScreen from '../screens/ReservedOrReturnScreen/OTPorQRScreen';
import GreetingScreen from '../screens/ReservedOrReturnScreen/GreetingScreen';
import CheckOutOrReturnScreen from '../screens/ReservedOrReturnScreen/CheckOutOrReturnScreen';
import EquipMentTypeScreen from '@/screens/EquipMentTypeScreen';
import ProductDetailScreen from '../screens/ProductDetailScreen';
import HelpMeChooseScreen from '@/screens/HelpMeChooseScreen';
import SelectBrandScreen from '@/screens/SelectBrand';
import selectBrandsBall from '@/screens/SelectBrands/selectBrandsBall';
import {fadeScreenOptions} from './transitions';
import BallsListScreen from '@/screens/BallsListScreen';
import ExperienceGuide from '@/screens/ExperienceGuide';
import BallDetailsScreen from '@/screens/BallDetailsScreen';
import StringingGrippingScreen from '@/screens/StringingGripping';
import zeroTotwoYearExperience from '@/screens/zeroTotwoYearExperience';
import twoPlusMoreExperience from '@/screens/twoPlusMoreExperience';
import TimerScreen from '@/screens/TimerScreen';
import BallsClickScreen from '@/screens/BallsClickScreen';
import CongratulationsScreen from '@/screens/CongratulationsScreen';
import slowAndSteady from '@/screens/SlowAndSteady';
import workoutWarrior from '@/screens/WorkoutWorrior';
import DispensingScreen from '@/screens/DispensingScreen';
import SomethingElseScreen from '@/screens/ReservedOrReturnScreen/SomethingElseScreen';
import StringsCustomizationScreen from '@/screens/StringsCustomization';
import WorkOnFinerPoints from '@/screens/WorkOnFinerPoints';
import PlayCompititily from '@/screens/PlayCompititily';
import SelectBrandsRacquet from '@/screens/Racquet/SelectBrandsRacquet';
import RacquetListScreen from '@/screens/Racquet/RacquetList';
import RacquetDetailsScreen from '@/screens/Racquet/RacqutDetails';
import ProductOptions from '@/screens/ProductOptions/ProductOptions';
import ProductOptionSelected from '@/screens/ProductOptions/ProductOptionSelected';
import RacquetRotationScreen from '@/screens/RacquetRotationScreen';
import TryItNowRacquetDetails from '@/screens/Racquet/TryItNowRacquetDetails';
import PurchaseRacquetDetails from '@/screens/Racquet/PurchaseRacquetDetails';
import CustomiseRacquetDetails from '@/screens/Racquet/CustomiseRacquetDetails';
import CheckOutScreen from '@/screens/Racquet/CheckOutScreen';
import HelpMeChooseRaquet from '@/screens/Racquet/HelpMeChooseRaquet';
import SelectBrandsPadel from '@/screens/Padel/SelectBrandsPadel';
import ModifyReservation from '@/screens/ReservedOrReturnScreen/ModifyReservation';
import ReturnScreen from '@/screens/ReservedOrReturnScreen/ReturnScreen';
import PadelListScreen from '@/screens/Padel/PadelList';
import PadelDetailsScreen from '@/screens/Padel/padelDetails';
import TryItNowPadelDetails from '@/screens/Padel/TryItNowPadelDetails';
import PurchasePadelDetails from '@/screens/Padel/PurchasePadelDetails';
import CustomisePurchaseDetails from '@/screens/Padel/CustomisePurchaseDetails';
import PadelCheckOutScreen from '@/screens/Padel/PadelCheckOutScreen';
import ReservePadelDetails from '@/screens/Padel/ReservePadelDetails';
import BallDetailsWithoutCarousel from '@/screens/Ball/BallDetailsWithoutCarousel';
import SelectBallByType from '@/screens/SelectBallByType';
import ThankYouScreen from '@/screens/ThankYouScreen';
import GripTypes from '@/screens/GripTypes';
import TestSDKScreen from '@/screens/TestSDKScreen';
// Define the stack navigator param list
export type RootStackParamList = {
  Splash: undefined;
  Main: undefined;
  TestSDK: undefined;
  RentDemoBuy: undefined;
  ReservedOrReturn: undefined;
  OTPorQRScreen: undefined;
  GreetingScreen: undefined;
  CheckOutOrReturnScreen:
    | {
        from?: string;
        item: {
          title: string;
          description: string;
          image: string;
          handleImage: ImageRequireSource;
        };
        selectedOption: string;
      }
    | undefined;
  Language: undefined;
  Faq: undefined;
  SelectSport: undefined;
  AppDemo: undefined;
  Welcome: undefined;
  ExperienceGuide: {from?: string} | undefined;
  zeroTotwoYearExperience: {from?: string} | undefined;
  twoPlusMoreExperience: {from?: string} | undefined;
  ProductDetail: {productId?: string};
  slowAndSteady: {from?: string} | undefined;
  workoutWarrior: {from?: string} | undefined;
  workOnFinerPoints: {from?: string} | undefined;
  playCompititily: {from?: string} | undefined;
  ProductOptions:
    | {
        from?: string;
        item: {
          title: string;
          description: string;
          image: string;
          colors: {value: string; label: string}[];
          handleImage: ImageRequireSource;
        };
      }
    | undefined;
  ProductOptionSelected:
    | {
        from: string;
        selectedOption: string;
        brand?: string;
        type?: string;
        item: {
          title: string;
          description: string;
          image: string;
          colors: {value: string; label: string}[];
          handleImage: ImageRequireSource;
        };
      }
    | undefined;
  RacquetRotation: undefined;
  SomethingElse: undefined;
  ModifyReservation: undefined;
  ReturnScreen: undefined;
  StringsCustomization: undefined;
  GripTypes: undefined;
};

// Create the stack navigator
const Stack = createStackNavigator<RootStackParamList>();

// Main navigation component
export const Navigation = () => {
  return (
    <View style={{flex: 1}}>
      <StatusBar hidden={!KioskConfig.display.showStatusBar} />
      <Stack.Navigator
        initialRouteName="Splash"
        screenOptions={{
          headerShown: false,
          ...fadeScreenOptions,
        }}>
        <Stack.Screen name="Splash" component={SplashScreen} />
        <Stack.Screen name="Main" component={MainNavigator} />
        <Stack.Screen name="Welcome" component={WelcomeScreen} />
        <Stack.Screen name="RentDemoBuy" component={RentDemoBuyScreen} />
        <Stack.Screen
          name="ReservedOrReturn"
          component={ReservedOrReturnScreen}
        />
        <Stack.Screen name="OTPorQRScreen" component={OTPorQRScreen} />
        <Stack.Screen name="GreetingScreen" component={GreetingScreen} />
        <Stack.Screen
          name="CheckOutOrReturnScreen"
          component={CheckOutOrReturnScreen}
        />
        <Stack.Screen name="Language" component={LanguageScreen} />
        <Stack.Screen name="Faq" component={FaqScreen} />
        <Stack.Screen name="SelectSport" component={SelectSport} />
        <Stack.Screen name="AppDemo" component={AppDemo} />
        <Stack.Screen name="ExperienceGuide" component={ExperienceGuide} />
        <Stack.Screen
          name="zeroTotwoYearExperience"
          component={zeroTotwoYearExperience}
        />
        <Stack.Screen
          name="twoPlusMoreExperience"
          component={twoPlusMoreExperience}
        />
        <Stack.Screen name="ProductDetail" component={ProductDetailScreen} />
        <Stack.Screen name="slowAndSteady" component={slowAndSteady} />
        <Stack.Screen name="workoutWarrior" component={workoutWarrior} />
        <Stack.Screen name="workOnFinerPoints" component={WorkOnFinerPoints} />
        <Stack.Screen name="playCompititily" component={PlayCompititily} />
        <Stack.Screen name="ProductOptions" component={ProductOptions} />
        <Stack.Screen name="SomethingElse" component={SomethingElseScreen} />
        <Stack.Screen name="ModifyReservation" component={ModifyReservation} />
        <Stack.Screen name="ReturnScreen" component={ReturnScreen} />
        <Stack.Screen
          name="ProductOptionSelected"
          component={ProductOptionSelected}
        />
        <Stack.Screen
          name="RacquetRotation"
          component={RacquetRotationScreen}
        />
        <Stack.Screen name="GripTypes" component={GripTypes} />
      </Stack.Navigator>
    </View>
  );
};

// Create a separate navigator for screens inside the layout
export type MainStackParamList = {
  Home: undefined;
  TestSDK: undefined;
  RentDemoBuy: undefined;
  ReservedOrReturn: undefined;
  OTPorQRScreen: undefined;
  GreetingScreen: {userData?: any} | undefined;
  Language: undefined;
  EquipMentType: {from?: string} | undefined;
  SelectBallByType: undefined;
  SelectSport: {from?: string} | undefined;
  SelectBrand: {from?: string} | undefined;
  CheckOutOrReturnScreen:
    | {
        from?: string;
        item: {
          title: string;
          description: string;
          image: string;
          handleImage: ImageRequireSource;
        };
        selectedOption: string;
      }
    | undefined;
  HelpMeChoose: {from?: string} | undefined;
  SelectBrandsBall: undefined;
  BallsList: {
    brand: string;
  };
  ExperienceGuide: {from?: string} | undefined;
  BallDetails: {
    ball: {
      id: number;
      image: number;
      title: string;
      bottomTitle: string;
      ballImage: number;
      description: string;
    };
    brand: string;
  };
  zeroTotwoYearExperience: {from?: string} | undefined;
  twoPlusMoreExperience: {from?: string} | undefined;
  Timer: undefined;
  BallsClick: undefined;
  Congratulations: undefined;
  slowAndSteady: {from?: string} | undefined;
  workoutWarrior: {from?: string} | undefined;
  Dispensing: {from?: string} | undefined;
  SomethingElse: {from?: string} | undefined;
  ProductDetail: {productId?: string};
  workOnFinerPoints: {from?: string} | undefined;
  playCompititily: {from?: string} | undefined;
  SelectBrandsRacquet: undefined;
  RacquetList: {
    brand?: string;
  };
  RacquetDetails: {
    racquetData?: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
    from?: string;
    brand?: string;
  };
  TryItNowRacquetDetails: {
    racquetData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
    brand?: string;
  };
  PurchaseRacquetDetails: {
    racquetData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  CustomiseRacquetDetails: {
    racquetData: {
      id?: number;
      image: number;
      title: string;
      type?: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  CheckOutScreen: {
    racquetData: {
      id?: number;
      image: number;
      title: string;
      type?: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  HelpMeChooseRaquet: undefined;
  SelectBrandsPadel: undefined;
  ModifyReservation: undefined;
  ReturnScreen: undefined;
  ProductOptions:
    | {
        from?: string;
        item: {
          title: string;
          description: string;
          image: ImageRequireSource;
          colors: {value: string; label: string}[];
          handleImage: ImageRequireSource;
        };
      }
    | undefined;
  ProductOptionSelected:
    | {
        from: string;
        selectedOption: string;
        item: {
          title: string;
          description: string;
          image: ImageRequireSource;
          colors: {value: string; label: string}[];
          handleImage: ImageRequireSource;
        };
        type: string;
        brand?: string;
      }
    | undefined;
  RacquetRotation:
    | {
        from: string;
        selectedOption: string;
        item: {
          title: string;
          description: string;
          image: ImageRequireSource;
          colors: {value: string; label: string}[];
          handleImage: ImageRequireSource;
        };
        type: string;
      }
    | undefined;
  PadelList: {
    brand: string;
  };
  PadelDetails: {
    padelData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  TryItNowPadelDetails: {
    padelData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  PurchasePadelDetails: {
    padelData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  CustomisePurchaseDetails: {
    padelData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  PadelCheckOutScreen: {
    padelData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  ReservePadelDetails: {
    padelData: {
      id: number;
      image: number;
      title: string;
      type: string;
      description: string;
      colors: {value: string; label: string}[];
    };
  };
  BallDetailsWithoutCarousel: {
    ballData: {
      id: number;
      image: number;
      title: string;
      description: string;
    };
    type?: string;
  };
  ThankYouScreen: undefined;
  StringingGripping: undefined;
  StringsCustomization: undefined;
  GripTypes: undefined;
};

const MainStack = createStackNavigator<MainStackParamList>();

// Main content navigator (with Layout wrapper)
export const MainNavigator = () => {
  return (
    <MainStack.Navigator
      screenOptions={{
        headerShown: false,
        ...fadeScreenOptions,
      }}>
      <MainStack.Screen name="Home" component={HomeScreen} />
      <MainStack.Screen name="TestSDK" component={TestSDKScreen} />
      <MainStack.Screen name="RentDemoBuy" component={RentDemoBuyScreen} />
      <MainStack.Screen
        name="ReservedOrReturn"
        component={ReservedOrReturnScreen}
      />
      <MainStack.Screen name="OTPorQRScreen" component={OTPorQRScreen} />
      <MainStack.Screen name="GreetingScreen" component={GreetingScreen} />
      <MainStack.Screen
        name="CheckOutOrReturnScreen"
        component={CheckOutOrReturnScreen}
      />
      <MainStack.Screen name="Language" component={LanguageScreen} />
      <MainStack.Screen name="ProductDetail" component={ProductDetailScreen} />
      <MainStack.Screen name="SelectBallByType" component={SelectBallByType} />
      <MainStack.Screen name="EquipMentType" component={EquipMentTypeScreen} />
      <MainStack.Screen name="SelectSport" component={SelectSport} />
      <MainStack.Screen name="SelectBrand" component={SelectBrandScreen} />
      <MainStack.Screen name="HelpMeChoose" component={HelpMeChooseScreen} />
      <MainStack.Screen name="SelectBrandsBall" component={selectBrandsBall} />
      <MainStack.Screen name="BallsList" component={BallsListScreen} />
      <MainStack.Screen name="ExperienceGuide" component={ExperienceGuide} />
      <MainStack.Screen name="BallDetails" component={BallDetailsScreen} />
      <MainStack.Screen
        name="zeroTotwoYearExperience"
        component={zeroTotwoYearExperience}
      />
      <MainStack.Screen
        name="twoPlusMoreExperience"
        component={twoPlusMoreExperience}
      />
      <MainStack.Screen name="Timer" component={TimerScreen} />
      <MainStack.Screen name="BallsClick" component={BallsClickScreen} />
      <MainStack.Screen
        name="Congratulations"
        component={CongratulationsScreen}
      />
      <MainStack.Screen name="slowAndSteady" component={slowAndSteady} />
      <MainStack.Screen name="workoutWarrior" component={workoutWarrior} />
      <MainStack.Screen name="Dispensing" component={DispensingScreen} />
      <MainStack.Screen name="SomethingElse" component={SomethingElseScreen} />
      <MainStack.Screen
        name="workOnFinerPoints"
        component={WorkOnFinerPoints}
      />
      <MainStack.Screen name="playCompititily" component={PlayCompititily} />
      <MainStack.Screen
        name="SelectBrandsRacquet"
        component={SelectBrandsRacquet}
      />
      <MainStack.Screen name="RacquetList" component={RacquetListScreen} />
      <MainStack.Screen
        name="RacquetDetails"
        component={RacquetDetailsScreen}
      />
      <MainStack.Screen
        name="TryItNowRacquetDetails"
        component={TryItNowRacquetDetails}
      />
      <MainStack.Screen
        name="PurchaseRacquetDetails"
        component={PurchaseRacquetDetails}
      />
      <MainStack.Screen
        name="CustomiseRacquetDetails"
        component={CustomiseRacquetDetails}
      />
      <MainStack.Screen name="CheckOutScreen" component={CheckOutScreen} />
      <MainStack.Screen
        name="HelpMeChooseRaquet"
        component={HelpMeChooseRaquet}
      />
      <MainStack.Screen
        name="SelectBrandsPadel"
        component={SelectBrandsPadel}
      />
      <MainStack.Screen
        name="ModifyReservation"
        component={ModifyReservation}
      />
      <MainStack.Screen name="ReturnScreen" component={ReturnScreen} />
      <MainStack.Screen name="ProductOptions" component={ProductOptions} />
      <MainStack.Screen
        name="ProductOptionSelected"
        component={ProductOptionSelected}
      />
      <MainStack.Screen
        name="RacquetRotation"
        component={RacquetRotationScreen}
      />
      <MainStack.Screen name="PadelList" component={PadelListScreen} />
      <MainStack.Screen name="PadelDetails" component={PadelDetailsScreen} />
      <MainStack.Screen
        name="TryItNowPadelDetails"
        component={TryItNowPadelDetails}
      />
      <MainStack.Screen
        name="PurchasePadelDetails"
        component={PurchasePadelDetails}
      />
      <MainStack.Screen
        name="CustomisePurchaseDetails"
        component={CustomisePurchaseDetails}
      />
      <MainStack.Screen
        name="PadelCheckOutScreen"
        component={PadelCheckOutScreen}
      />
      <MainStack.Screen
        name="ReservePadelDetails"
        component={ReservePadelDetails}
      />
      <MainStack.Screen
        name="BallDetailsWithoutCarousel"
        component={BallDetailsWithoutCarousel}
      />
      <MainStack.Screen name="ThankYouScreen" component={ThankYouScreen} />
      <MainStack.Screen
        name="StringingGripping"
        component={StringingGrippingScreen}
      />
      <MainStack.Screen
        name="StringsCustomization"
        component={StringsCustomizationScreen}
      />
      <MainStack.Screen name="GripTypes" component={GripTypes} />
    </MainStack.Navigator>
  );
};

export default Navigation;
