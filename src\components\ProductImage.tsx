import React from 'react';
import {ImageRequireSource, View, ViewStyle} from 'react-native';
import FastImage, {ImageStyle} from 'react-native-fast-image';

const ProductImage = ({
  image,
  style,
  imageStyle,
}: {
  image: ImageRequireSource;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
}) => {
  return (
    <View style={style}>
      <FastImage source={image} style={imageStyle} resizeMode="contain" />
    </View>
  );
};

export default ProductImage;
