import CustomButton from '@/components/CustomButton';
import GradientBackground from '@/components/GradientBackground';
import Typography from '@/components/Typography';
import images from '@/config/images';
import KioskConfig from '@/config/KioskConfig';
import {MainStackParamList} from '@/navigation';
import {useLanguageStore} from '@/store/languageStore';
import {scale, verticalScale} from '@/utils/scale';
import {selectBrandBallActions} from '@/utils/staticData';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import {ScrollView} from 'react-native-gesture-handler';

const selectBrandsBall = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();

  const handleNavigation = (data: ActionButton) => {
    if (data.screenName) {
      navigation.navigate(data.screenName, {brand: data.brand});
    }
  };

  return (
    <GradientBackground>
      <View style={[styles.container]}>
        <View style={[styles.contentContainer]}>
          <Typography
            variant="faqAnswer"
            color={KioskConfig.theme.colors.white}>
            {t('rentDemoBuy.selectBrand')}
          </Typography>
          <ScrollView>
            <View style={[styles.buttonContainer]}>
              {selectBrandBallActions?.actions.map(data => (
                <CustomButton
                  text={data.title}
                  onPress={() => handleNavigation(data)}
                  key={data.id}
                  style={styles.btnStyle}
                  buttonContentContainerStyle={styles.buttonContentContainer}
                  brand={data.brand}
                />
              ))}
            </View>
          </ScrollView>
        </View>
        <View style={styles.imageContainer}>
          <FastImage
            source={selectBrandBallActions.image}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1440),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    margin: 'auto',
    paddingBottom: scale(100),
  },
  btnStyle: {
    width: scale(594),
    justifyContent: 'center',
    alignItems: 'flex-start',
    height: verticalScale(104),
  },

  contentContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },

  searchTitle: {
    marginBottom: scale(0),
  },
  buttonContainer: {
    display: 'flex',
    gap: scale(20),
    marginTop: scale(24),
  },
  image: {
    width: scale(712.75),
    height: verticalScale(746.17),
  },
  buttonContentContainer: {
    gap: scale(25),
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: 0,
  },
});

export default selectBrandsBall;
