import React, {useState} from 'react';
import GradientBackground from '../../components/GradientBackground';
import {Image, StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import CustomCalendar from '@/components/CustomCalendar';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';
interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

export type ProductOptionsProps = {
  route: RouteProp<MainStackParamList, 'ReservePadelDetails'>;
};

export type ProductOptionSelectedProps = {
  title: string;
  description: string;
  actionButtons: ActionButton[];
  radioTitle: string;
  counterTitle: string;
  calendar?: () => React.ReactNode;
};

const ReservePadelDetails = ({route}: ProductOptionsProps) => {
  const padelData = route.params?.padelData;
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const {t} = useLanguageStore();
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  const handleNavigation = () => {
    navigation.navigate('CheckOutOrReturnScreen', {from: 'padel'});
  };

  const actionArray = [
    {
      id: 1,
      title: 'Reserve',
      onPress: () => {
        handleNavigation();
      },
    },
    {
      id: 2,
      title: 'Purchase',
      onPress: () => {
        handleNavigation();
      },
    },
  ];

  const actionButtons: ActionButton[] = actionArray;

  const getCalendar = () => (
    <View>
      <CustomCalendar
        selectedDate={selectedDate}
        onSelectDate={setSelectedDate}
        minDate={new Date()} // Disables dates before today
      />
    </View>
  );

  const renderBallDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            btnStyle={styles.btnStyles}
            title={t(padelData?.title || '')}
            description={t(padelData?.description || '')}
            actionButtons={actionButtons}
            calendar={() => getCalendar()}
            colors={padelData?.colors}
          />
        </View>
        <View style={styles.imageContainer}>
          <Image
            source={padelData?.image}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderBallDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    margin: 'auto',
    flexDirection: 'row',
    position: 'relative',
    justifyContent: 'space-between',
    flex: 1,
  },
  contentContainer: {
    width: '35%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    height: '100%',
  },
  imageContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    flex: 1,
  },
  image: {
    width: scale(577),
    height: verticalScale(961),
  },
  btnStyles: {
    width: scale(385),
    height: verticalScale(75),
  },
  ballImage: {
    width: 283,
    height: 280,
  },
  ballImageContainer: {
    position: 'absolute',
    right: '-40%',
    bottom: '-7%',
  },
});

export default ReservePadelDetails;
