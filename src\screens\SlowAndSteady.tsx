import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import {ComfortableRacquetData, PowerRacquetData} from '@/config/staticData';
import {MainStackParamList} from '@/navigation';
import {useLanguageStore} from '@/store/languageStore';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';

export type slowAndSteadyProps = {
  route: RouteProp<MainStackParamList, 'slowAndSteady'>;
};

const SlowAndSteady = ({route}: slowAndSteadyProps) => {
  const {from} = route.params || {};
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        description={t('slowAndSteady.description')}
        btnText1={{
          text: t('slowAndSteady.btnText1_1'),
          text2: t('slowAndSteady.btnText1_2'),
        }}
        btnText2={{
          text: t('slowAndSteady.btnText2_1'),
          text2: '',
        }}
        onPress={() => {
          navigation.navigate('RacquetDetails', {
            from: from,
            racquetData: ComfortableRacquetData,
          });
        }}
        onPress2={() => {
          navigation.navigate('RacquetDetails', {
            from: from,
            racquetData: PowerRacquetData,
          });
        }}
      />
    </GradientBackground>
  );
};

export default SlowAndSteady;
