import React from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '@/navigation';
import {gripSizesData} from '@/config/staticData';
import ProductImage from '@/components/ProductImage';
import {StackNavigationProp} from '@react-navigation/stack';
import {scale, verticalScale} from '@/utils/scale';
import images from '@/config/images';
import {useLanguageStore} from '@/store/languageStore';
interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

const CheckOutScreen = ({
  route,
}: {
  route: RouteProp<MainStackParamList, 'CheckOutScreen'>;
}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const {racquetData} = route.params || {};

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: t('checkout.checkout'),
      onPress: () => {
        navigation.navigate('RacquetRotation', {
          from: 'racquet',
          selectedOption: 'Checkout',
          item: {
            ...racquetData,
            handleImage: images.dunlopCx200Handle,
          },
          type: 'brandList',
        });
      },
    },
    {
      id: 2,
      title: t('checkout.continueShopping'),
      onPress: () => {
        navigation.navigate('RentDemoBuy');
      },
    },
  ];

  const renderRacquetDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            title={t(racquetData?.title)}
            description={t(racquetData?.description)}
            actionButtons={actionButtons}
            gripSizes={gripSizesData}
            colors={racquetData?.colors}
            helpMeChoosePress={() => navigation.navigate('HelpMeChooseRaquet')}
          />
        </View>
        <View style={styles.imageContainer}>
          <ProductImage image={racquetData?.image} imageStyle={styles.image} />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderRacquetDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    marginHorizontal: 'auto',
    flexDirection: 'row',
    position: 'relative',
    flex: 1,
  },
  contentContainer: {
    width: scale(489),
    height: verticalScale(1050),
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: scale(100),
    top: verticalScale(100),
  },
  image: {
    width: scale(353),
    height: verticalScale(961),
  },
  ballImage: {
    width: 283,
    height: 280,
  },
  ballImageContainer: {
    position: 'absolute',
    right: '-40%',
    bottom: '-7%',
  },
});
export default CheckOutScreen;
