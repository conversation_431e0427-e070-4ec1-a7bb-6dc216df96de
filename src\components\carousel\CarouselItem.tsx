import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  SharedValue,
} from 'react-native-reanimated';
import {scale} from '@/utils/scale';
import FastImage from 'react-native-fast-image';

export interface CarouselItemProps {
  item: {
    image: string;
    ar?: number; // aspect ratio
    [key: string]: any;
  };
  index: number;
  translateX: SharedValue<number>;
  itemWidth?: number;
  itemGap?: number;
  onPress?: (index: number) => void;
}

const CarouselItem: React.FC<CarouselItemProps> = ({
  item,
  index,
  translateX,
  itemWidth = scale(300),
  itemGap = 20,
  onPress,
}) => {
  // Create animated style for the card
  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    // Calculate input range directly inside the worklet function
    const position = -translateX.value;
    const inputRange = [
      (index - 1) * (itemWidth + itemGap), // Previous card
      index * (itemWidth + itemGap), // Current card
      (index + 1) * (itemWidth + itemGap), // Next card
    ];

    // Scale animation
    const scaleValue = interpolate(position, inputRange, [0.9, 1, 0.9]);

    // Opacity animation
    const opacity = interpolate(position, inputRange, [0.7, 1, 0.7]);

    return {
      transform: [{scale: scaleValue}],
      opacity,
    };
  });

  // Calculate image height based on aspect ratio
  const imageHeight = itemWidth * (1 / (item.ar || 1));

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      onPress={() => onPress && onPress(index)}
      style={[styles.container, {width: itemWidth}]}>
      <Animated.View style={[styles.card, animatedStyle]}>
        <FastImage
          source={{uri: item.image}}
          style={[
            styles.image,
            {
              height: imageHeight,
            },
          ]}
          resizeMode="cover"
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: scale(10),
  },
  card: {
    borderRadius: scale(16),
    overflow: 'hidden',
    backgroundColor: '#fff',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  image: {
    width: '100%',
    borderRadius: scale(16),
  },
});

export default CarouselItem;
