import { I18n } from 'i18n-js';

// Import translations
import en from '../translations/en.json';
import es from '../translations/es.json';

// Create translations object
const translations = {
  en,
  es,
};

// Create i18n instance
const i18n = new I18n(translations);

// Set default locale and fallback
i18n.defaultLocale = 'en';
i18n.locale = 'en';
i18n.enableFallback = true;

export default i18n;
