/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  StyleSheet,
  Platform,
  TouchableWithoutFeedback,
  BackHandler,
  AppState,
  AppStateStatus,
  View,
  Animated,
  StatusBar,
} from 'react-native';
import 'react-native-gesture-handler'; // Required for React Navigation
import 'react-native-reanimated'; // Required for Reanimated
import Navigation from './src/navigation';
import InactivityScreen from './src/screens/InactivityScreen';
import {NavigationContainer, DefaultTheme} from '@react-navigation/native';
import * as Sentry from '@sentry/react-native';
import IdleTimerManager from 'react-native-idle-timer';
import {clearTokenStorage} from '@/services/api';
import {useLanguageStore} from '@/store/languageStore';
import {
  cleanupTokenRefresh,
  initializeTokenRefresh,
} from '@/utils/tokenRefreshManager';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppProvider} from './src/context/AppContext';

Sentry.init({
  dsn: 'https://<EMAIL>/4509354293985280',

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,
  debug: true,
  enabled: __DEV__ ? false : true,
  // Configure Session Replay
  replaysSessionSampleRate: 0,
  replaysOnErrorSampleRate: 0,
  // integrations: [Sentry.mobileReplayIntegration()],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

/**
 * Timer for inactivity detection
 */
// 5 minutes in milliseconds (300,000ms)
const INACTIVITY_TIMEOUT = 300000;

/**
 * Kiosk application for 32-inch HD display (1920x1080)
 */
function App(): React.JSX.Element {
  const [showInactivityScreen, setShowInactivityScreen] = useState(false);
  const inactivityOpacity = useState(new Animated.Value(0))[0];
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  // Function to handle logout
  const handleLogout = useCallback(() => {
    // Clear token storage
    clearTokenStorage();

    // The actual logout mutation will be handled in the InactivityScreen component
    // which is wrapped in the QueryClientProvider
  }, []);

  // Function to reset the inactivity timer
  const resetInactivityTimer = useCallback(() => {
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }

    lastActivityRef.current = Date.now();

    inactivityTimerRef.current = setTimeout(() => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityRef.current;

      // Only show inactivity screen if we've been inactive for the full timeout
      if (timeSinceLastActivity >= INACTIVITY_TIMEOUT) {
        // Handle logout due to inactivity
        handleLogout();

        // Show inactivity screen
        setShowInactivityScreen(true);

        // Enable the idle timer to keep screen on during video playback
        IdleTimerManager.setIdleTimerDisabled(true, 'inactivityVideo');

        // Animate the inactivity screen in
        Animated.timing(inactivityOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      } else {
        // If there was activity during the timeout, reset the timer
        resetInactivityTimer();
      }
    }, INACTIVITY_TIMEOUT);
  }, [inactivityOpacity, handleLogout]);

  // Apply kiosk mode settings when app starts
  useEffect(() => {
    // Lock the device in kiosk mode
    if (Platform.OS === 'android') {
      BackHandler.addEventListener('hardwareBackPress', () => {
        // Block the back button in kiosk mode
        return true;
      });
    }

    // Set up app state change listener
    const appStateSubscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (nextAppState === 'active') {
          resetInactivityTimer();
        }
      },
    );

    // Initialize the timer
    resetInactivityTimer();

    return () => {
      // Cleanup
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }

      // Disable the idle timer when component unmounts
      IdleTimerManager.setIdleTimerDisabled(false, 'inactivityVideo');

      // Clean up app state listener
      appStateSubscription.remove();
    };
  }, [resetInactivityTimer]);

  // Initialize translations when app starts
  useEffect(() => {
    const initializeTranslations = async () => {
      const {currentLanguage, updateTranslations} = useLanguageStore.getState();
      await updateTranslations(currentLanguage);
    };

    initializeTranslations();
  }, []);

  // Hide status bar and system navigation
  useEffect(() => {
    // Hide status bar
    StatusBar.setHidden(true);

    // For Android: Hide navigation bar
    if (Platform.OS === 'android') {
      // Set navigation bar color to transparent
      // This helps to ensure the app content expands into navigation bar area
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
  }, []);

  // Reset inactivity timer on user interaction
  const handleUserInteraction = useCallback(() => {
    if (showInactivityScreen) {
      // Don't reset timer here, let the inactivity screen handle it
      return;
    }

    lastActivityRef.current = Date.now();
    resetInactivityTimer();
  }, [showInactivityScreen, resetInactivityTimer]);

  // Handle continue from inactivity screen
  const handleContinue = useCallback(() => {
    // Disable the idle timer when returning from inactivity
    IdleTimerManager.setIdleTimerDisabled(false, 'inactivityVideo');

    // Animate the inactivity screen out
    Animated.timing(inactivityOpacity, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start(() => {
      setShowInactivityScreen(false);
    });

    // Reset the timer
    lastActivityRef.current = Date.now();
    resetInactivityTimer();
  }, [inactivityOpacity, resetInactivityTimer]);

  // Custom theme with transparent background for better transitions
  const navigationTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      background: 'transparent',
    },
  };

  // Token refresh lifecycle
  useEffect(() => {
    initializeTokenRefresh();
    return () => cleanupTokenRefresh();
  }, []);

  return (
    <AppProvider>
      <NavigationContainer theme={navigationTheme}>
        <SafeAreaView style={styles.safeArea} edges={['top', 'bottom']}>
          <TouchableWithoutFeedback onPress={handleUserInteraction}>
            <View style={styles.container}>
              <Navigation />
              {showInactivityScreen && (
                <Animated.View
                  style={[
                    styles.inactivityOverlay,
                    {
                      opacity: inactivityOpacity,
                      // When opacity is 0, make sure touches pass through
                      // Set pointerEvents to 'none' when opacity is 0, and 'auto' when it's 1
                      pointerEvents: showInactivityScreen ? 'auto' : 'none',
                    },
                  ]}>
                  <InactivityScreen onContinue={handleContinue} />
                </Animated.View>
              )}
            </View>
          </TouchableWithoutFeedback>
        </SafeAreaView>
      </NavigationContainer>
    </AppProvider>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#000',
  },
  container: {
    flex: 1,
  },
  inactivityOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
});

export default Sentry.wrap(App);
