import CustomButton from '@/components/CustomButton';
import GradientBackground from '@/components/GradientBackground';
import Typography from '@/components/Typography';
import images from '@/config/images';
import KioskConfig from '@/config/KioskConfig';
import {MainStackParamList} from '@/navigation';
import {useLanguageStore} from '@/store/languageStore';
import {scale, verticalScale} from '@/utils/scale';
import {selectBrandRacquetActions} from '@/utils/staticData';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
interface RouteParams {
  from?: string;
}
const SelectBrandsRacquet = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const {from} = route.params || {};
  const {t} = useLanguageStore();

  return (
    <GradientBackground>
      <View style={[styles.container]}>
        <View style={[styles.contentContainer]}>
          <Typography
            variant="faqAnswer"
            color={KioskConfig.theme.colors.white}>
            {t('selectBrandRacquet.title')}
          </Typography>
          <View style={[styles.buttonContainer]}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                gap: scale(26),
              }}>
              {selectBrandRacquetActions.map(data => (
                <CustomButton
                  text=""
                  onPress={() => {
                    if (from === 'gripping') {
                      navigation.navigate('GripTypes', {
                        brand: data.brand,
                      });
                    } else {
                      navigation.navigate('RacquetList', {
                        brand: data.brand,
                      });
                    }
                  }}
                  key={data.id}
                  style={styles.btnStyle}
                  buttonContentContainerStyle={styles.buttonContentContainer}
                  brand={data.brand}
                />
              ))}
            </ScrollView>
          </View>
        </View>
        <View style={styles.imageContainer}>
          <FastImage
            source={images.racquet}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxWidth: scale(1440),
    margin: 'auto',
    alignItems: 'center',
    flexDirection: 'row',
    position: 'relative',
  },
  contentContainer: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: scale(100),
    top: 0,
  },
  btnStyle: {
    width: scale(594),
    height: verticalScale(104),
    justifyContent: 'center',
    alignItems: 'flex-start',
  },

  buttonContainer: {
    display: 'flex',
    gap: scale(26),
    marginTop: scale(26),
  },
  image: {
    width: scale(534),
    height: verticalScale(1089),
  },
  buttonContentContainer: {
    gap: scale(25),
    alignItems: 'center',
  },
});

export default SelectBrandsRacquet;
