/**
 * Example component demonstrating how to use the scaling utilities
 *
 * This file shows practical examples of using the scale, verticalScale,
 * moderateScale functions and the spacing/typography constants.
 */
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {scale, verticalScale, moderateScale} from './scale';
import {spacing, typography, createResponsiveStyles} from './dimensions';

const ScalingExample: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Example of using scale directly in styles */}
      <View style={styles.box}>
        <Text style={styles.text}>Using scale directly</Text>
      </View>

      {/* Example of using spacing constants */}
      <View style={styles.spacingExample}>
        <Text style={styles.text}>Using spacing constants</Text>
      </View>

      {/* Example of using typography constants */}
      <Text style={styles.heading}>Heading Text</Text>
      <Text style={styles.title}>Title Text</Text>
      <Text style={styles.body}>Body Text</Text>

      {/* Example of using createResponsiveStyles helper */}
      <View style={styles.responsiveBox}>
        <Text style={styles.text}>Using createResponsiveStyles</Text>
      </View>
    </View>
  );
};

// Example styles using the scaling utilities
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.m,
    alignItems: 'center',
    justifyContent: 'center',
  },
  box: {
    width: scale(200),
    height: verticalScale(100),
    backgroundColor: 'lightblue',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(8),
    marginBottom: spacing.m,
  },
  spacingExample: {
    padding: spacing.m,
    margin: spacing.l,
    backgroundColor: 'lightgreen',
    borderRadius: scale(8),
    marginBottom: spacing.m,
  },
  text: {
    fontSize: scale(16),
    textAlign: 'center',
  },
  heading: {
    fontSize: typography.heading,
    fontWeight: 'bold',
    marginBottom: spacing.s,
  },
  title: {
    fontSize: typography.title,
    fontWeight: '600',
    marginBottom: spacing.s,
  },
  body: {
    fontSize: typography.md,
    marginBottom: spacing.m,
  },
  // Using the createResponsiveStyles helper
  responsiveBox: {
    ...createResponsiveStyles({
      width: scale(300),
      height: verticalScale(150),
      padding: scale(20),
      borderRadius: scale(12),
      backgroundColor: 'lightsalmon',
      justifyContent: 'center',
      alignItems: 'center',
    }),
  },
});

export default ScalingExample;
