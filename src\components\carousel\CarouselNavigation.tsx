import React from 'react';
import {StyleSheet, Pressable} from 'react-native';
import Animated from 'react-native-reanimated';
import ArrowLeft from '../ArrowLeft';
import ArrowRight from '../ArrowRight';
import {scale} from '@/utils/scale';

interface CarouselNavigationProps {
  isFirstItem: boolean;
  isLastItem: boolean;
  onPrevious: () => void;
  onNext: () => void;
}

const CarouselNavigation: React.FC<CarouselNavigationProps> = ({
  isFirstItem,
  isLastItem,
  onPrevious,
  onNext,
}) => {
  return (
    <Animated.View style={styles.buttonRow}>
      <Pressable
        disabled={isFirstItem}
        onPress={onPrevious}
        style={({pressed}) => [
          styles.navButton,
          pressed && styles.navButtonPressed,
        ]}>
        <Animated.View>
          <ArrowLeft
            stroke={isFirstItem ? '#cbd5e1' : 'black'} // bg-gray-400
          />
        </Animated.View>
      </Pressable>

      <Pressable
        disabled={isLastItem}
        onPress={onNext}
        style={({pressed}) => [
          styles.navButton,
          pressed && styles.navButtonPressed,
        ]}>
        <Animated.View>
          <ArrowRight
            stroke={isLastItem ? '#cbd5e1' : 'black'} // bg-gray-400
          />
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  buttonRow: {
    paddingHorizontal: scale(16),
    flexDirection: 'row',
    paddingTop: scale(20),
    justifyContent: 'center',
  },
  navButton: {
    marginHorizontal: scale(8),
    padding: scale(12),
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: scale(12),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: scale(4),
  },
  navButtonPressed: {
    backgroundColor: '#f3f4f6', // gray-100
    borderRadius: scale(12),
  },
});

export default CarouselNavigation;
