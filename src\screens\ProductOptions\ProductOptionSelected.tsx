import React, {useState} from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {gripSizesData} from '@/config/staticData';
import CustomCalendar from '@/components/CustomCalendar';
import {scale, verticalScale, moderateScale} from '@/utils/scale';
import FastImage from 'react-native-fast-image';
import {FONTS} from '@/utils/fonts';
import {useLanguageStore} from '@/store/languageStore';
interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

export type ProductOptionsProps = {
  route: RouteProp<MainStackParamList, 'ProductOptionSelected'>;
};

export type ProductOptionSelectedProps = {
  title: string;
  description: string;
  actionButtons: ActionButton[];
  radioTitle: string;
  counterTitle: string;
  calendar?: () => React.ReactNode;
};

type ItemProps = {
  title: string;
  description: string;
  image: number;
  colors: {value: string; label: string}[];
  handleImage: number;
};

type ParamProps = {
  item?: ItemProps;
  from?: string;
  selectedOption?: string;
  type?: string;
  brand?: string;
};
const ProductOptionSelected = ({route}: ProductOptionsProps) => {
  const {item, from, selectedOption, type, brand}: ParamProps =
    route.params || {};
  const [selectedDate, setSelectedDate] = useState(new Date());

  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const handleNavigation = (selectedOption: string) => {
    if (!item) return;

    if (selectedOption === 'Standard') {
      navigation.navigate('CheckOutScreen', {
        racquetData: item,
      });
    } else if (selectedOption === 'Customize') {
      navigation.navigate('CustomiseRacquetDetails', {
        racquetData: item,
      });
    } else {
      navigation.navigate('RacquetRotation', {
        from: from || '',
        selectedOption,
        item,
        type: type || '',
      });
    }
  };

  const actionArray =
    selectedOption === 'TryNow'
      ? [
          {
            id: 1,
            title: t('productOptionSelected.checkout'),
            onPress: () => {
              handleNavigation('Checkout');
            },
          },
          {
            id: 2,
            title: t('productOptionSelected.continueShopping'),
            onPress: () => {
              handleNavigation('Shopping');
            },
          },
        ]
      : selectedOption === 'Purchase'
        ? [
            {
              id: 1,
              title: 'Standard',
              onPress: () => {
                handleNavigation('Standard');
              },
            },
            {
              id: 2,
              title: 'Customize',
              onPress: () => {
                handleNavigation('Customize');
              },
            },
          ]
        : [
            {
              id: 1,
              title: 'Reserve',
              onPress: () => {
                handleNavigation('Reserve');
              },
            },
            {
              id: 2,
              title: 'Purchase',
              onPress: () => {
                handleNavigation('Purchase');
              },
            },
          ];

  const actionButtons: ActionButton[] = actionArray;

  const color = [
    {
      value: '#0050A4',
      label: '#0050A4',
    },
  ];

  const getCalendar = () => (
    <View>
      <CustomCalendar
        selectedDate={selectedDate}
        onSelectDate={setSelectedDate}
        minDate={new Date()} // Disables dates before today
      />
    </View>
  );

  const props: ProductOptionSelectedProps = {
    title: t(item?.title || ''),
    description: t(item?.description || ''),
    actionButtons: actionButtons,
    radioTitle: 'Select one',
    counterTitle: 'Quantity',
  };

  if (selectedOption === 'Reserve') {
    props.calendar = () => getCalendar();
  }

  const renderBallDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            // btnStyle={styles.btnStyles}
            gripSizes={gripSizesData}
            // textStyle={styles.textStyle}
            colors={item?.colors || color}
            {...props}
            brand={brand}
          />
        </View>
        <View style={styles.imageContainer}>
          <FastImage
            source={item?.image}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderBallDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    marginHorizontal: 'auto',
    flexDirection: 'row',
    position: 'relative',
    flex: 1,
  },
  contentContainer: {
    width: scale(489),
    maxHeight: verticalScale(1050),
  },
  imageContainer: {
    position: 'absolute',
    flex: 1,
    right: scale(100),
    top: verticalScale(100),
  },
  image: {
    width: scale(353),
    height: verticalScale(961),
  },
});

export default ProductOptionSelected;
