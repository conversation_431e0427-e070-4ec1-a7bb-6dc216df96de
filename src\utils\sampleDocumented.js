/**
 * @fileoverview Sample documented utility functions for JSDoc testing
 * @module Utils/SampleDocumented
 * @category Utils
 */

/**
 * Calculates the sum of two numbers
 * @function add
 * @param {number} a - The first number
 * @param {number} b - The second number
 * @returns {number} The sum of a and b
 * @example
 * const result = add(5, 3); // returns 8
 */
export const add = (a, b) => {
  return a + b;
};

/**
 * Formats a string with proper capitalization
 * @function formatString
 * @param {string} str - The string to format
 * @returns {string} The formatted string
 * @example
 * const formatted = formatString('hello world'); // returns 'Hello World'
 */
export const formatString = (str) => {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Configuration object for the sample module
 * @constant {Object}
 * @property {string} version - The module version
 * @property {boolean} debug - Whether debug mode is enabled
 */
export const CONFIG = {
  version: '1.0.0',
  debug: false
};

/**
 * @typedef {Object} User
 * @property {string} id - User unique identifier
 * @property {string} name - User display name
 * @property {string} email - User email address
 * @property {boolean} [active=true] - Whether the user is active
 */

/**
 * Creates a new user object
 * @function createUser
 * @param {string} name - The user's name
 * @param {string} email - The user's email
 * @returns {User} The created user object
 * @example
 * const user = createUser('John Doe', '<EMAIL>');
 */
export const createUser = (name, email) => {
  return {
    id: Math.random().toString(36).substr(2, 9),
    name,
    email,
    active: true
  };
};
