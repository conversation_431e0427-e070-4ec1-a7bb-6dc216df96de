import {AppState, AppStateStatus} from 'react-native';
import axios from 'axios';
import {jwtDecode} from 'jwt-decode';
import {logError} from '@/utils/sentryUtils';
import {API_BASE_URL, tokenStorage} from '@/services/api';
import {useAuthStore} from '@/store/authStore';

let currentAppState: AppStateStatus = 'active';
let proactiveRefreshTimer: NodeJS.Timeout | null = null;
let appStateSubscription: {remove: () => void} | null = null;

interface JwtPayload {
  exp: number;
  [key: string]: any;
}

const isTokenExpiringSoon = (
  token: string,
  thresholdInSeconds = 120,
): boolean => {
  try {
    const decoded = jwtDecode<JwtPayload>(token);
    const now = Math.floor(Date.now() / 1000);
    return decoded.exp - now < thresholdInSeconds;
  } catch {
    return true; // Treat as expiring soon if decoding fails
  }
};

// Function to get email from auth store
const getEmailFromStore = () => {
  const store = useAuthStore.getState();
  return store.user?.email;
};

const refreshTokens = async () => {
  const refresh_token = tokenStorage.getString('refreshToken');
  console.log('email ===>', email);
  if (!refresh_token) return;
  const email = getEmailFromStore();

  try {
    const response = await axios.post(`${API_BASE_URL}/refresh-token`, {
      refresh_token,
      email,
    });
    console.log('response ==>', response);

    const {accessToken, refreshToken: newRefreshToken} = response.data.data;

    tokenStorage.set('accessToken', accessToken);
    tokenStorage.set('refreshToken', newRefreshToken);
  } catch (error) {
    logError(error as Error, {context: 'TokenRefresh'});
  }
};

const handleAppStateChange = async (nextAppState: AppStateStatus) => {
  if (
    currentAppState.match(/inactive|background/) &&
    nextAppState === 'active'
  ) {
    const accessToken = tokenStorage.getString('accessToken');
    if (accessToken && isTokenExpiringSoon(accessToken)) {
      await refreshTokens();
    }
  }
  currentAppState = nextAppState;
};

const startProactiveRefresh = () => {
  if (proactiveRefreshTimer) return;

  proactiveRefreshTimer = setInterval(
    async () => {
      const accessToken = tokenStorage.getString('accessToken');
      if (accessToken && isTokenExpiringSoon(accessToken)) {
        await refreshTokens();
      }
    },
    5 * 60 * 1000,
  ); // Every 5 minutes
};

const stopProactiveRefresh = () => {
  if (proactiveRefreshTimer) {
    clearInterval(proactiveRefreshTimer);
    proactiveRefreshTimer = null;
  }
};

export const initializeTokenRefresh = () => {
  appStateSubscription = AppState.addEventListener(
    'change',
    handleAppStateChange,
  );
  startProactiveRefresh();
};

export const cleanupTokenRefresh = () => {
  if (appStateSubscription) {
    appStateSubscription.remove();
    appStateSubscription = null;
  }
  stopProactiveRefresh();
};
