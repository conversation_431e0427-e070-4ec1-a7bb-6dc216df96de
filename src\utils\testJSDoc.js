/**
 * @fileoverview Test file to verify JSDoc module generation
 * @module Utils/TestJSDoc
 * @category Utilities
 */

/**
 * A simple test function to verify JSDoc is working
 * @function testFunction
 * @param {string} message - The message to return
 * @returns {string} The formatted message
 * @example
 * // Returns "Hello: World"
 * testFunction("World");
 */
export function testFunction(message) {
  return `Hello: ${message}`;
}

/**
 * A test class to verify class documentation
 * @class TestClass
 * @classdesc A simple test class for JSDoc verification
 */
export class TestClass {
  /**
   * Create a TestClass instance
   * @param {string} name - The name for this instance
   */
  constructor(name) {
    /**
     * The name of this instance
     * @type {string}
     */
    this.name = name;
  }

  /**
   * Get a greeting message
   * @method greet
   * @returns {string} A greeting message
   */
  greet() {
    return `Hello from ${this.name}`;
  }
}

/**
 * A test constant
 * @constant {string} TEST_CONSTANT
 * @default
 */
export const TEST_CONSTANT = "This is a test constant";

/**
 * Default export object with configuration
 * @namespace TestConfig
 * @property {string} version - The version number
 * @property {boolean} enabled - Whether the feature is enabled
 */
const TestConfig = {
  version: "1.0.0",
  enabled: true
};

export default TestConfig;
