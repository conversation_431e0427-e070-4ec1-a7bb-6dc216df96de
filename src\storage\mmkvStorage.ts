/**
 * @fileoverview MMKV Storage Configuration - High-performance storage solution for React Native
 * @module Storage/MMKVStorage
 * @category Storage
 */

import {MMKV} from 'react-native-mmkv';

/**
 * Main MMKV instance for the application.
 * MMKV is a high-performance key-value storage framework.
 *
 * @constant {MMKV} mmkv
 * @example
 * import { mmkv } from '@/storage/mmkvStorage';
 *
 * // Store a value
 * mmkv.set('user_id', '12345');
 *
 * // Retrieve a value
 * const userId = mmkv.getString('user_id');
 */
export const mmkv = new MMKV();

/**
 * Zustand-compatible storage adapter for MMKV.
 * Provides a standardized interface for Zustand state persistence.
 *
 * @constant {Object} zustandStorage
 * @property {Function} getItem - Retrieves a value by key
 * @property {Function} setItem - Stores a value with a key
 * @property {Function} removeItem - Removes a value by key
 *
 * @example
 * import { zustandStorage } from '@/storage/mmkvStorage';
 * import { create } from 'zustand';
 * import { persist } from 'zustand/middleware';
 *
 * const useStore = create(
 *   persist(
 *     (set) => ({
 *       count: 0,
 *       increment: () => set((state) => ({ count: state.count + 1 })),
 *     }),
 *     {
 *       name: 'counter-storage',
 *       storage: zustandStorage,
 *     }
 *   )
 * );
 */
export const zustandStorage = {
  getItem: (key: string): string | null => {
    const value = mmkv.getString(key);
    return value ?? null;
  },
  setItem: (key: string, value: string): void => {
    mmkv.set(key, value);
  },
  removeItem: (key: string): void => {
    mmkv.delete(key);
  },
};
