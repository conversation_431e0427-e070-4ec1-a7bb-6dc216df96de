# Kiosk Application

A React Native kiosk application optimized for 32-inch HD displays (1920x1080).

## Features

- Fullscreen kiosk mode
- Welcome screen with start button
- Home screen with example options
- Screen timeout prevention
- Inactivity detection and reset
- Hardware back button blocking (Android)
- Optimized for 32" HD displays

## Setup Instructions

### Prerequisites

- Node.js (>= 18.0.0)
- Yarn or npm
- React Native development environment
- Android SDK for Android deployment
- Xcode for iOS deployment

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/kiosk-app.git
cd kiosk-app
```

2. Install dependencies:

```bash
yarn install
# or
npm install
```

3. Install the required Android/iOS dependencies:

```bash
# For iOS
cd ios && pod install && cd ..
```

### Code Style and Linting

This project uses ESLint and Prettier for code quality and consistent formatting.

#### Automatic Formatting on Save

VSCode is configured to automatically format files and fix linting errors when you save:

1. Install the recommended VSCode extensions:

   - ESLint: `dbaeumer.vscode-eslint`
   - Prettier: `esbenp.prettier-vscode`

2. ESLint errors will be automatically fixed and code will be formatted when you press Ctrl+S.

#### Manual Commands

You can also run formatting and linting manually:

```bash
# Format all files with Prettier
yarn format

# Fix ESLint errors
yarn lint:fix

# Run both formatting and linting fixes
yarn fix-all
```

### Running the Application

#### Development Mode

To run the application in development mode:

```bash
# Start the Metro server
yarn start

# Run on Android
yarn android

# Run on iOS
yarn ios
```

#### Production Build

To create a production build:

```bash
# For Android
yarn build:android

# The APK will be available at: android/app/build/outputs/apk/release/app-release.apk
```

### Cleaning Up and Distribution

To clean the project of build artifacts and large files before sharing or archiving:

1. Use the included cleanup script:

```bash
# Make the script executable if needed
chmod +x clean.sh

# Run the cleanup script
./clean.sh
```

2. Or create a clean zip file:

```bash
# Make the script executable if needed
chmod +x create-clean-zip.sh

# Create a clean zip file
./create-clean-zip.sh
```

The clean-up process removes:

- node_modules
- lock files (yarn.lock, package-lock.json)
- Android build artifacts (.so files, .dex files, build directories)
- iOS build artifacts (build, Pods)
- Other large temporary files

This will significantly reduce the repository size from ~450MB to a few MB.

## Kiosk Mode Setup

### Android

To set up a device in kiosk mode:

1. Install the APK on your device.
2. Enable Developer Options by tapping Build Number 7 times in the Settings > About Phone menu.
3. Enable USB debugging in Developer Options.
4. Use Android Device Policy Controller (DPC) or a Mobile Device Management (MDM) solution to set the app as a kiosk app.

Alternatively, on Android 9+, you can use the following ADB commands to enable kiosk mode:

```bash
# Replace com.yourcompany.kioskapp with your app's package name
adb shell dpm set-device-owner com.yourcompany.kioskapp/.DeviceAdminReceiver
```

## Configuration

You can configure the kiosk behavior by modifying the `src/config/KioskConfig.ts` file:

- Display settings (resolution, orientation, status bar)
- Application settings (name, default page, inactivity timeout)
- Theme settings (colors, font sizes)

## Customization

### Adding New Screens

1. Create a new screen component in the `src/screens` directory.
2. Import it in `App.tsx`.
3. Add the necessary state and navigation logic in `App.tsx`.

### Styling

The application uses a centralized theme defined in `src/config/KioskConfig.ts`. Update this file to change the overall appearance of the app.

## License

MIT
