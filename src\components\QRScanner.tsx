import React, {useState, useCallback, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
  useCameraPermission,
} from 'react-native-vision-camera';
import KioskConfig from '@/config/KioskConfig';
import {moderateScale, scale, verticalScale} from '@/utils/scale';
import {useKioskLogin} from '@/hooks/queries/useAuth';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {User} from '@/store/authStore';

interface QRScannerProps {
  onScan?: (data: string) => void; // Made optional since we'll handle API calls internally
  onClose?: () => void;
  isActive?: boolean;
  directLogin?: boolean; // If true, will directly call the kiosk login API
  onLoginSuccess?: (userData?: User) => void; // Callback for successful login with user data
  onLoginError?: (error: string) => void; // Callback for login error
}

const {width} = Dimensions.get('window');
const frameSize = width / 1.9;

const QRScanner: React.FC<QRScannerProps> = ({
  onClose,
  isActive = true,
  directLogin = false,
  onLoginSuccess,
  onLoginError,
}) => {
  const [scannerActive, setScannerActive] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const {hasPermission, requestPermission} = useCameraPermission();
  const [permissionRequested, setPermissionRequested] = useState(false);

  // Use the kiosk login mutation
  const kioskLoginMutation = useKioskLogin();

  // Request camera permission only when the component is active
  useEffect(() => {
    if (isActive && !hasPermission && !permissionRequested) {
      setPermissionRequested(true);
      requestPermission();
    }
  }, [isActive, hasPermission, permissionRequested, requestPermission]);

  // Reset scanner state when isActive changes
  useEffect(() => {
    if (isActive) {
      setTimeout(() => {
        setScannerActive(true);
        setIsProcessing(false);
      }, 200);
    }
  }, [isActive]);

  // Get the front camera device
  const device = useCameraDevice('front');

  // Function to reset scanner state
  const resetScanner = useCallback(() => {
    setScannerActive(true);
    setIsProcessing(false);
  }, []);

  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    if (hasPermission) {
      setTimeout(() => {
        setIsMounted(true);
      }, 1200);
    }
  }, [hasPermission]);

  // Handle QR code scan
  const handleScan = useCallback(
    (token: string) => {
      setIsProcessing(true);

      if (directLogin) {
        // Call the kiosk login API with callbacks
        kioskLoginMutation.mutate(token, {
          onSuccess: response => {
            // Extract user data from response
            const userData = response.data;

            // Handle success
            if (response.data) {
              onLoginSuccess?.(userData);
            } else {
              // Navigate to greeting screen if no success callback provided
              onLoginError(response?.message || 'Login failed');
            }
            // Reset scanner
            resetScanner();
          },
          onError: error => {
            // Handle error
            const errorMessage =
              error instanceof Error ? error.message : 'Login failed';
            if (onLoginError) {
              onLoginError(errorMessage);
            }
            // Reset scanner after error
            setTimeout(() => {
              resetScanner();
            }, 2000);
          },
        });
      } else {
        // If no handlers provided, just reset the scanner
        setTimeout(() => {
          resetScanner();
        }, 2000);
      }
    },
    [
      directLogin,
      kioskLoginMutation,
      onLoginError,
      onLoginSuccess,
      resetScanner,
    ],
  );

  // Set up the code scanner
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: codes => {
      if (codes.length > 0 && scannerActive && !isProcessing) {
        setScannerActive(false);
        if (codes[0].value) {
          handleScan(codes[0].value);
        }
      }
    },
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      position: 'relative',
      overflow: 'hidden',
    },
    camera: {
      alignSelf: 'center',
      // flex: 1,
      width: scale(1000),
      height: verticalScale(850),
    },
    markerContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999,
      ...StyleSheet.absoluteFillObject,
    },
    scanFrame: {
      zIndex: 9999,
      width: scale(1050),
      height: verticalScale(910),
      position: 'relative',
    },
    corner: {
      position: 'absolute',
      width: 40,
      height: 40,
      borderColor: KioskConfig.theme.colors.text.secondary,
      borderWidth: 3,
    },
    cornerTL: {
      top: 0,
      left: 0,
      borderBottomWidth: 0,
      borderRightWidth: 0,
      borderTopLeftRadius: 20,
    },
    cornerTR: {
      top: 0,
      right: 0,
      borderBottomWidth: 0,
      borderLeftWidth: 0,
      borderTopRightRadius: 20,
    },
    cornerBL: {
      bottom: 0,
      left: 0,
      borderTopWidth: 0,
      borderRightWidth: 0,
      borderBottomLeftRadius: 20,
    },
    cornerBR: {
      bottom: 0,
      right: 0,
      borderTopWidth: 0,
      borderLeftWidth: 0,
      borderBottomRightRadius: 20,
    },
    scanText: {
      color: KioskConfig.theme.colors.white,
      marginTop: scale(20),
      fontSize: scale(16),
      textAlign: 'center',
    },
    closeButton: {
      position: 'absolute',
      bottom: scale(40),
      alignSelf: 'center',
      padding: scale(15),
      zIndex: 10,
    },
    closeText: {
      color: KioskConfig.theme.colors.white,
      fontSize: scale(16),
    },
    permissionContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: KioskConfig.theme.colors.background,
    },
    permissionText: {
      color: KioskConfig.theme.colors.text.secondary,
      fontSize: 16,
      textAlign: 'center',
      marginBottom: scale(20),
    },
    permissionButton: {
      backgroundColor: KioskConfig.theme.colors.primary,
      paddingVertical: scale(10),
      paddingHorizontal: scale(20),
      borderRadius: scale(8),
    },
    permissionButtonText: {
      color: KioskConfig.theme.colors.white,
      fontSize: scale(16),
    },
    loadingContainer: {
      ...StyleSheet.absoluteFillObject,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 20,
    },
    loadingText: {
      color: KioskConfig.theme.colors.white,
      fontSize: scale(16),
      marginTop: scale(10),
    },
  });

  // If not active, show a placeholder
  if (!isActive) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Switch to Direct Connect tab to scan QR codes
        </Text>
      </View>
    );
  }

  // If active but no permission, show a request screen
  if (!hasPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Camera permission is required to scan QR codes
        </Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // If no camera device is available
  if (device == null) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>No camera device available</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isMounted && (
        <Camera
          style={styles.camera}
          device={device}
          isActive={isActive && hasPermission && !isProcessing}
          codeScanner={codeScanner}
          enableZoomGesture={false}
          isMirrored={true}
          video={false}
        />
      )}
      <View style={styles.markerContainer}>
        <View style={styles.scanFrame}>
          <View style={[styles.cornerTL, styles.corner]} />
          <View style={[styles.cornerTR, styles.corner]} />
          <View style={[styles.cornerBL, styles.corner]} />
          <View style={[styles.cornerBR, styles.corner]} />
        </View>
      </View>

      {/* Loading overlay */}
      {(isProcessing || kioskLoginMutation.isPending) && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={KioskConfig.theme.colors.white}
          />
          <Text style={styles.loadingText}>Processing...</Text>
        </View>
      )}

      {onClose && !isProcessing && !kioskLoginMutation.isPending && (
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={[styles.closeText]} accessibilityRole="text">
            Cancel
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default QRScanner;
