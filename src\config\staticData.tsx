import images from './images';

export const appDemoSteps = [
  {
    id: 1,
    title: 'Welcome to Our App',
    image: images.appDemo1,
  },
  {
    id: 2,
    title: 'Explore Features',
    image: images.appDemo2,
  },
  {
    id: 3,
    title: 'Discover New Features',
    image: images.appDemo3,
  },
  {
    id: 4,
    title: 'Customize Your Experience',
    image: images.appDemo4,
  },
  {
    id: 5,
    title: 'Connect with Others',
    image: images.appDemo5,
  },
  {
    id: 6,
    title: 'Share Your Moments',
    image: images.appDemo6,
  },
  {
    id: 7,
    title: 'Track Your Progress',
    image: images.appDemo7,
  },
  {
    id: 8,
    title: 'Set Your Goals',
    image: images.appDemo8,
  },
  {
    id: 9,
    title: 'View Your Statistics',
    image: images.appDemo9,
  },
  {
    id: 10,
    title: 'Manage Your Profile',
    image: images.appDemo10,
  },
  {
    id: 11,
    title: 'Explore Categories',
    image: images.appDemo11,
  },
  {
    id: 12,
    title: 'Find New Content',
    image: images.appDemo12,
  },
  {
    id: 13,
    title: 'Save Your Favorites',
    image: images.appDemo13,
  },
  {
    id: 14,
    title: 'Create Collections',
    image: images.appDemo14,
  },
  {
    id: 15,
    title: 'Share with Friends',
    image: images.appDemo15,
  },
  {
    id: 16,
    title: 'Join Communities',
    image: images.appDemo16,
  },
  {
    id: 17,
    title: 'Participate in Events',
    image: images.appDemo17,
  },
  {
    id: 18,
    title: 'Get Notifications',
    image: images.appDemo18,
  },
  {
    id: 19,
    title: 'Customize Settings',
    image: images.appDemo19,
  },
  {
    id: 20,
    title: 'Manage Privacy',
    image: images.appDemo20,
  },
  {
    id: 21,
    title: 'Explore Premium Features',
    image: images.appDemo21,
  },
  {
    id: 22,
    title: 'Upgrade Your Account',
    image: images.appDemo22,
  },
  {
    id: 23,
    title: 'Access Exclusive Content',
    image: images.appDemo23,
  },
  {
    id: 24,
    title: 'Join Premium Groups',
    image: images.appDemo24,
  },
  {
    id: 25,
    title: 'Get Priority Support',
    image: images.appDemo25,
  },
  {
    id: 26,
    title: 'Use Advanced Tools',
    image: images.appDemo26,
  },
  {
    id: 27,
    title: 'Access Analytics',
    image: images.appDemo27,
  },
  {
    id: 28,
    title: 'Create Custom Reports',
    image: images.appDemo28,
  },
  {
    id: 29,
    title: 'Export Your Data',
    image: images.appDemo29,
  },
  {
    id: 30,
    title: 'Backup Your Content',
    image: images.appDemo30,
  },
  {
    id: 31,
    title: 'Sync Across Devices',
    image: images.appDemo31,
  },
  {
    id: 32,
    title: 'Use Offline Mode',
    image: images.appDemo32,
  },
  {
    id: 33,
    title: 'Manage Storage',
    image: images.appDemo33,
  },
  {
    id: 34,
    title: 'Clear Cache',
    image: images.appDemo34,
  },
  {
    id: 35,
    title: 'Update App',
    image: images.appDemo35,
  },
  {
    id: 36,
    title: 'Check for Updates',
    image: images.appDemo36,
  },
  {
    id: 37,
    title: 'View Release Notes',
    image: images.appDemo37,
  },
  {
    id: 38,
    title: 'Get Started Now',
    image: images.appDemo38,
  },
];

export const gripSizesData = [
  {
    value: '4 1/8',
    label: '4 1/8',
  },
  {
    value: '4 1/4',
    label: '4 1/4',
  },
  {
    value: '4 3/8',
    label: '4 3/8',
  },
];

export const ComfortableRacquetData = {
  id: 1,
  image: images.dunlopCx200,
  title: 'comfortableRacquetData.title',
  type: 'Control',
  description: 'comfortableRacquetData.description',
  colors: [
    {
      value: '#FD725D',
      label: '#FD725D',
    },
  ],
};

export const PowerRacquetData = {
  id: 2,
  image: images.dunlopSx300,
  title: 'powerRacquetData.title',
  type: 'Spin',
  description: 'powerRacquetData.description',
  colors: [
    {
      value: '#CEE936',
      label: '#CEE936',
    },
  ],
};
