import React from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import KioskConfig from '../config/KioskConfig';
import {RootStackParamList} from '../navigation';
import GradientBackground from '../components/GradientBackground';
import images from '../config/images';
import {FONTS} from '@/utils/fonts';
import {scale, verticalScale, moderateScale} from '@/utils/scale';
import {createResponsiveStyles} from '@/utils/dimensions';
import FastImage from 'react-native-fast-image';
import {useLanguageStore} from '../store/languageStore';
// Import AppDemo for preloading
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import AppDemo from '../screens/AppDemo';

type WelcomeScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Welcome'
>;

const WelcomeScreen: React.FC = () => {
  const {colors, fontSize} = KioskConfig.theme;
  const navigation = useNavigation<WelcomeScreenNavigationProp>();
  const {t} = useLanguageStore();

  // Using AppDemo import to preload it
  // This is a common pattern to ensure assets are loaded before they're needed
  // The import at the top of the file is enough to preload the module

  return (
    <GradientBackground
      headerOnly
      homeButton={false}
      backButton={false}
      useGradient={false}
      backgroundColor="#000000">
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <FastImage
            source={images.fullLogo}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text
            style={[
              styles.subtitle,
              {
                color: colors.text.secondary,
                fontSize: scale(fontSize.xlarge2),
                fontFamily: FONTS.cocogoose,
              },
            ]}>
            {t('welcome.title')}
          </Text>
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.kioskButton]}
            onPress={() => navigation.navigate('Main')}>
            <Text style={styles.buttonText}>{t('welcome.start')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.demoButton]}
            onPress={() => {
              // Use requestAnimationFrame to improve touch responsiveness
              requestAnimationFrame(() => {
                navigation.navigate('AppDemo');
              });
            }}>
            <Text style={styles.buttonText}>{t('welcome.appDemo')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#000',
    paddingBottom: verticalScale(40),
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Using scale for width and height to maintain aspect ratio
  logo: {
    ...createResponsiveStyles({
      width: 630,
      height: 200,
    }),
  },
  subtitle: {
    color: '#FFF',
    textAlign: 'center',
    fontFamily: FONTS.cocogoose,
    fontWeight: '400',
    // letterSpacing: -1.029,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: scale(20),
    width: '100%',
    paddingHorizontal: scale(40),
  },
  button: {
    flex: 1,
    paddingVertical: verticalScale(16),
    paddingHorizontal: scale(32),
    borderRadius: scale(30),
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: scale(200),
  },
  kioskButton: {
    backgroundColor: '#FFFFFF',
  },
  demoButton: {
    backgroundColor: '#FFFFFF',
  },
  buttonText: {
    fontSize: scale(20),
    fontWeight: '600',
    color: '#000000',
    textTransform: 'uppercase',
  },
});

export default WelcomeScreen;
