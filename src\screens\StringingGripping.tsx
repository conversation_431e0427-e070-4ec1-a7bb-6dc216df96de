import React from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import CustomButton from '../components/CustomButton';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {stringingGrippingButtonConfigs} from '@/utils/staticData';
import {useLanguageStore} from '@/store/languageStore';
import {scale, verticalScale} from '@/utils/scale';

const StringingGrippingScreen = ({route}: {route: any}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const handleNavigate = (config: any) => {
    if (config.screenName === 'StringsCustomization') {
      navigation.navigate(config.screenName, {
        from: 'stringing',
      });
    } else if (config.screenName === 'SelectSport') {
      navigation.navigate(config.screenName, {
        from: 'gripping',
      });
    }
    // else {
    //   navigation.navigate(config.screenName, {
    //     ballData: BallsData[0],
    //   });
    // }
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <Typography variant="faqAnswer" color={KioskConfig.theme.colors.white}>
          {t('stringingGripping.select')}
        </Typography>
        <View style={styles.buttonContainer}>
          {stringingGrippingButtonConfigs.map((config, index) => (
            <CustomButton
              key={index}
              text={t(config.text)}
              style={styles.button}
              onPress={() => handleNavigate(config)}
            />
          ))}
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: scale(100),
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: scale(55),
    marginTop: scale(40),
  },
  titleContainer: {
    alignSelf: 'flex-start',
  },
  button: {
    minWidth: scale(520),
    height: verticalScale(172),
  },
});
export default StringingGrippingScreen;
