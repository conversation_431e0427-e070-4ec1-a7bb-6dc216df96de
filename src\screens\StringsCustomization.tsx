import React, {useEffect, useState} from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import CustomButton from '../components/CustomButton';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {useLanguageStore} from '@/store/languageStore';
import {scale, verticalScale} from '@/utils/scale';

interface ActionButton {
  id: number;
  title: string;
  dropdown?: boolean;
  items?: Array<{label: string; value: string}>;
  onSelectItem?: (item: {label: string; value: string}) => void;
  highlighted?: boolean;
}

const StringsCustomizationScreen = ({route}: {route: any}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const [selectedString, setSelectedString] = useState<{
    first: string;
    second: string;
  }>({
    first: 'Strings',
    second: 'Tension',
  });
  const {t} = useLanguageStore();
  const handleNavigate = (config: any) => {
    // if (config.screenName === 'EquipMentType') {
    //   navigation.navigate(config.screenName, {
    //     from: from,
    //   });
    // } else {
    //   navigation.navigate(config.screenName, {
    //     ballData: BallsData[0],
    //   });
    // }
  };

  useEffect(() => {
    const isFirstSelection = selectedString.first !== 'Strings';
    const isSecondSelection = selectedString.second !== 'Tension';
    if (isFirstSelection && isSecondSelection) {
      const timer = setTimeout(() => {
        navigation.navigate('CheckOutOrReturnScreen', {from: 'stringing'});
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [selectedString, navigation]);

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: selectedString.first,
      dropdown: true,
      items: [
        {label: 'Unstrung - Do not string', value: 'unstrung'},
        {label: 'String Specials', value: 'string_specials'},
        {label: 'Favorite Value Strings', value: 'favorite_value_strings'},
        {label: 'Favorite Comfort Strings', value: 'favorite_comfort_strings'},
        {label: 'Favorite Spin Strings', value: 'favorite_spin_strings'},
        {
          label: 'Favorite Durability Strings',
          value: 'favorite_durability_strings',
        },
        {label: 'Favorite Natural Guts', value: 'favorite_natural_guts'},
        {label: 'Favorite Multifilaments', value: 'favorite_multifilaments'},
        {label: 'Favorite Synthetic Guts', value: 'favorite_synthetic_guts'},
        {
          label: 'Favorite Value Polyesters',
          value: 'favorite_value_polyesters',
        },
        {label: 'Favorite Soft Polyesters', value: 'favorite_soft_polyesters'},
        {label: 'Best Of The Rest!', value: 'best_of_the_rest'},
        {label: 'Build a Custom Hybrid', value: 'build_custom_hybrid'},
        {label: 'String Not In This Menu', value: 'string_not_in_menu'},
      ],
      onSelectItem: (item: {label: string; value: string}) => {
        setSelectedString(prev => ({
          ...prev,
          first: item.label,
        }));
      },
      highlighted: selectedString.first !== 'Strings',
    },
    {
      id: 2,
      title: selectedString.second,
      dropdown: true,
      items: [
        {label: '50 lbs', value: '50'},
        {label: '51 lbs', value: '51'},
        {label: '52 lbs', value: '52'},
        {label: '53 lbs', value: '53'},
        {label: '54 lbs', value: '54'},
        {label: '55 lbs', value: '55'},
        {label: '56 lbs', value: '56'},
        {label: '57 lbs', value: '57'},
        {label: '58 lbs', value: '58'},
        {label: '59 lbs', value: '59'},
        {label: '60 lbs', value: '60'},
        {label: '61 lbs', value: '61'},
        {label: '62 lbs', value: '62'},
        {label: '63 lbs', value: '63'},
        {label: '64 lbs', value: '64'},
        {label: '65 lbs', value: '65'},
      ],
      onSelectItem: (item: {label: string; value: string}) => {
        setSelectedString(prev => ({
          ...prev,
          second: item.label,
        }));
      },
      highlighted: selectedString.second !== 'Tension',
    },
  ];

  return (
    <GradientBackground>
      <View style={styles.container}>
        <Typography variant="faqAnswer" color={KioskConfig.theme.colors.white}>
          {t('stringingGripping.select')}
        </Typography>
        <View style={styles.buttonContainer}>
          {actionButtons.map((config, index) => (
            <CustomButton
              key={index}
              text={config.title}
              style={styles.button}
              onPress={() => handleNavigate(config)}
              dropdown={config.dropdown}
              dropdownItems={config.items}
              onSelectItem={config.onSelectItem}
              highlighted={config.highlighted}
            />
          ))}
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: scale(100),
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: scale(55),
    marginTop: scale(40),
  },
  titleContainer: {
    alignSelf: 'flex-start',
  },
  button: {
    minWidth: scale(520),
    height: verticalScale(172),
  },
});
export default StringsCustomizationScreen;
