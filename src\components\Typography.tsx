import React from 'react';
import {Text, StyleSheet, TextStyle, TextProps} from 'react-native';
import {FONTS} from '../utils/fonts';
import {FONT_SIZE} from '../utils/fontSizes';
import {moderateScale, scale, verticalScale} from '@/utils/scale';

interface TypographyProps extends TextProps {
  children: React.ReactNode;
  variant?:
    | 'heading'
    | 'heading1'
    | 'heading2'
    | 'userTitle'
    | 'title'
    | 'sectionTitle'
    | 'subtitle'
    | 'body'
    | 'caption'
    | 'button'
    | 'permissionTitle'
    | 'badgeText'
    | 'subtitle2'
    | 'bodyMedium'
    | 'bodyMedium1'
    | 'parkTitle'
    | 'subTitle3'
    | 'subTitle4'
    | 'communitySubDetail'
    | 'moreText'
    | 'tagTitle'
    | 'cocogooseRegular'
    | 'faqTitle'
    | 'faqAnswer'
    | 'dunlopTitle'
    | 'dunlopDescription'
    | 'RadioTitle'
    | 'helpMeChoose'
    | 'ballTitle'
    | 'customButton'
    | 'radioLabel'
    | 'counterLabel'
    | 'timerText'
    | 'timer'
    | 'dispensingText'
    | 'congratulationsText'
    | 'racquetTitle'
    | 'racquetType'
    | 'tryText'
    | 'gripSize'
    | 'toggleTab'
    | 'appliedText'
    | 'appliedText2'
    | 'offText'
    | 'offText2';
  color?: string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  style?: TextStyle | TextStyle[];
}

/***
 * USAGE
 *
      <View style={styles.section}>
        <Typography variant="heading" style={styles.typographyItem}>
          Heading - Helvetica Neue Bold (32px)
        </Typography>
        <Typography variant="title" style={styles.typographyItem}>
          Title - Helvetica Neue Bold (28px)
        </Typography>
        <Typography variant="sectionTitle" style={styles.typographyItem}>
          Section Title - Helvetica Neue Medium (22px)
        </Typography>
        <Typography variant="subtitle" style={styles.typographyItem}>
          Subtitle - Helvetica Neue Medium (18px)
        </Typography>
        <Typography variant="subtitle2" style={styles.typographyItem}>
          Subtitle 2 - Helvetica Neue Medium (24px)
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="body" style={styles.typographyItem}>
          Body - Helvetica Neue Regular (14px) - This is the default body text
          style used for most content in the app.
        </Typography>
        <Typography variant="bodyMedium" style={styles.typographyItem}>
          Body Medium - Helvetica Neue Medium (16px) - Similar to body but with
          medium weight and larger size.
        </Typography>
        <Typography variant="bodyMedium1" style={styles.typographyItem}>
          Body Medium 1 - Helvetica Neue Regular (14px) - This variant has a
          smaller line height.
        </Typography>
        <Typography variant="caption" style={styles.typographyItem}>
          Caption - Helvetica Neue Regular (12px) - Used for secondary or
          supporting text.
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="button" style={styles.typographyItem}>
          Button - Helvetica Neue Medium (14px)
        </Typography>
        <Typography variant="badgeText" style={styles.typographyItem}>
          Badge Text - Helvetica Neue Bold (14px)
        </Typography>
        <Typography variant="permissionTitle" style={styles.typographyItem}>
          Permission Title - Helvetica Neue Bold (18px)
        </Typography>
        <Typography variant="parkTitle" style={styles.typographyItem}>
          Park Title - Helvetica Neue Bold (18px)
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography variant="cocogooseRegular" style={styles.typographyItem}>
          Cocogoose Regular - Primary Cocogoose font (14px)
        </Typography>
        <Typography
          variant="cocogooseRegular"
          style={[styles.typographyItem, {fontSize: 18}]}>
          Cocogoose Regular - Custom size (18px)
        </Typography>
        <Typography variant="subTitle3" style={styles.typographyItem}>
          SubTitle 3 - Cocogoose Regular (22px)
        </Typography>
        <Typography variant="subTitle4" style={styles.typographyItem}>
          SubTitle 4 - Helvetica Neue Bold (20px)
        </Typography>
        <Typography variant="communitySubDetail" style={styles.typographyItem}>
          Community Sub Detail - Helvetica Neue Medium (14px)
        </Typography>
        <Typography variant="moreText" style={styles.typographyItem}>
          More Text - Helvetica Neue Medium (12px)
        </Typography>
        <Typography variant="tagTitle" style={styles.typographyItem}>
          Tag Title - Helvetica Neue Medium (10px)
        </Typography>
      </View>

      <View style={styles.section}>
        <Typography
          variant="heading"
          color="#1E88E5"
          align="center"
          style={styles.typographyItem}>
          Custom Color and Alignment
        </Typography>
        <Typography
          variant="body"
          style={[styles.typographyItem, styles.customStyle]}>
          Typography with custom styles
        </Typography>
      </View>
 */

/**
 * Typography component for consistent text styling across the app
 */
const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body',
  color,
  align,
  style,
  ...rest
}) => {
  const getVariantStyle = () => {
    switch (variant) {
      case 'userTitle':
        return styles.userTitle;
      case 'heading':
        return styles.heading;
      case 'heading1':
        return styles.heading1;
      case 'heading2':
        return styles.heading2;
      case 'title':
        return styles.title;
      case 'sectionTitle':
        return styles.sectionTitle;
      case 'subtitle':
        return styles.subtitle;
      case 'body':
        return styles.body;
      case 'caption':
        return styles.caption;
      case 'button':
        return styles.button;
      case 'badgeText':
        return styles.badgeText;
      case 'permissionTitle':
        return styles.permissionTitle;
      case 'subtitle2':
        return styles.subtitle2;
      case 'bodyMedium':
        return styles.bodyMedium;
      case 'bodyMedium1':
        return styles.bodyMedium1;
      case 'parkTitle':
        return styles.parkTitle;
      case 'subTitle3':
        return styles.subTitle3;
      case 'subTitle4':
        return styles.subTitle4;
      case 'communitySubDetail':
        return styles.communitySubDetail;
      case 'moreText':
        return styles.moreText;
      case 'tagTitle':
        return styles.tagTitle;
      case 'cocogooseRegular':
        return styles.cocogooseRegular;
      case 'faqTitle':
        return styles.faqTitle;
      case 'faqAnswer':
        return styles.faqAnswer;
      case 'dunlopTitle':
        return styles.dunlopTitle;
      case 'dunlopDescription':
        return styles.dunlopDescription;
      case 'RadioTitle':
        return styles.RadioTitle;
      case 'helpMeChoose':
        return styles.helpMeChoose;
      case 'ballTitle':
        return styles.ballTitle;
      case 'customButton':
        return styles.customButton;
      case 'radioLabel':
        return styles.radioLabel;
      case 'counterLabel':
        return styles.counterLabel;
      case 'timerText':
        return styles.timerText;
      case 'timer':
        return styles.timer;
      case 'dispensingText':
        return styles.dispensingText;
      case 'congratulationsText':
        return styles.congratulationsText;
      case 'racquetTitle':
        return styles.racquetTitle;
      case 'racquetType':
        return styles.racquetType;
      case 'tryText':
        return styles.tryText;
      case 'gripSize':
        return styles.gripSize;
      case 'toggleTab':
        return styles.toggleTab;
      case 'appliedText':
        return styles.appliedText;
      case 'appliedText2':
        return styles.appliedText2;
      case 'offText':
        return styles.offText;
      case 'offText2':
        return styles.offText2;
      default:
        return styles.body;
    }
  };

  return (
    <Text
      style={[
        getVariantStyle(),
        align && {textAlign: align},
        color && {color},
        style,
      ]}
      {...rest}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  userTitle: {
    fontFamily: FONTS.light,
    fontSize: scale(FONT_SIZE.userTitle),
    lineHeight: scale(96),
  },
  heading: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.heading),
    lineHeight: scale(40),
    letterSpacing: 0.25,
  },
  heading1: {
    fontFamily: FONTS.regular,
    fontSize: scale(FONT_SIZE.heading1),
    letterSpacing: 0.25,
    fontWeight: '300',
  },
  heading2: {
    fontFamily: FONTS.cocogooseLight,
    fontSize: scale(FONT_SIZE.heading2),
    lineHeight: scale(55),
    letterSpacing: -0.03,
  },
  title: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.title),
    lineHeight: scale(36),
  },
  sectionTitle: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.xxxl),
    lineHeight: scale(28),
    letterSpacing: 0.15,
  },
  subtitle: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.xl),
    lineHeight: scale(28),
    letterSpacing: 0.15,
  },
  body: {
    fontFamily: FONTS.regular,
    fontSize: scale(FONT_SIZE.md),
    lineHeight: scale(24),
    letterSpacing: 0.5,
  },
  caption: {
    fontFamily: FONTS.regular,
    fontSize: scale(FONT_SIZE.sm),
    lineHeight: scale(20),
    letterSpacing: 0.4,
  },
  button: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.md),
    lineHeight: scale(24),
    letterSpacing: 1.25,
    textTransform: 'uppercase',
  },
  permissionTitle: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.xl),
    lineHeight: scale(28),
    letterSpacing: 0.15,
  },
  badgeText: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.md),
    lineHeight: scale(16),
  },
  subtitle2: {
    fontFamily: FONTS.regular,
    fontSize: scale(FONT_SIZE.subTitle),
    lineHeight: scale(24),
    letterSpacing: 0.25,
    fontWeight: '300',
  },
  bodyMedium: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.lg),
    lineHeight: scale(24),
    letterSpacing: 0.5,
  },
  bodyMedium1: {
    fontFamily: FONTS.regular,
    fontSize: scale(FONT_SIZE.md),
    lineHeight: scale(16),
    letterSpacing: 0.5,
  },
  parkTitle: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.xl),
    lineHeight: scale(28),
    letterSpacing: 0.15,
  },
  subTitle3: {
    fontFamily: FONTS.cocogoose,
    fontSize: scale(FONT_SIZE.xxxl),
    lineHeight: scale(35),
    letterSpacing: 0.25,
  },
  subTitle4: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.xxl),
    lineHeight: scale(25),
    letterSpacing: 0.25,
  },
  communitySubDetail: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.md),
    lineHeight: scale(15),
    letterSpacing: 0.4,
  },
  moreText: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.sm),
    lineHeight: scale(12),
    letterSpacing: 0.4,
  },
  tagTitle: {
    fontFamily: FONTS.medium,
    fontSize: scale(FONT_SIZE.xs),
    lineHeight: scale(20),
    letterSpacing: 0.4,
  },
  cocogooseRegular: {
    fontFamily: FONTS.cocogoose,
    fontSize: scale(FONT_SIZE.md),
    lineHeight: scale(24),
    letterSpacing: 0.5,
  },
  faqTitle: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.heading2),
    lineHeight: scale(48),
    letterSpacing: 0.5,
    // fontWeight: '400',
  },
  faqAnswer: {
    fontFamily: FONTS.cocogooseLight,
    fontSize: scale(FONT_SIZE.heading2),
    lineHeight: scale(48),
    letterSpacing: 0.5,
    fontWeight: '300',
  },
  customButton: {
    fontFamily: FONTS.cocogooseLight,
    fontSize: scale(FONT_SIZE.customButton),
    lineHeight: scale(48),
    letterSpacing: 0.5,
    fontWeight: '400',
  },
  dunlopTitle: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(27),
    lineHeight: scale(28),
    letterSpacing: 0.5,
    fontWeight: '500',
  },
  dunlopDescription: {
    fontFamily: FONTS.light,
    fontSize: scale(17),
    lineHeight: scale(18.5),
    letterSpacing: -0.3,
    fontWeight: '400',
  },
  RadioTitle: {
    fontFamily: FONTS.cocogoose,
    fontSize: FONT_SIZE.xl2,
    lineHeight: 28.64,
    letterSpacing: 0.5,
    fontWeight: '500',
  },
  helpMeChoose: {
    fontFamily: FONTS.cocogoose,
    fontSize: scale(FONT_SIZE.xl2),
    lineHeight: scale(28.64),
    letterSpacing: 0.5,
    fontWeight: '400',
  },
  ballTitle: {
    fontFamily: FONTS.cocogoose,
    fontSize: scale(FONT_SIZE.heading0),
    lineHeight: scale(32.46),
    fontWeight: '400',
    letterSpacing: -0.03,
  },

  radioLabel: {
    fontFamily: FONTS.cocogooseLight,
    fontSize: scale(FONT_SIZE.xl3),
    lineHeight: scale(29.6),
    letterSpacing: 0.5,
    fontWeight: '400',
  },

  counterLabel: {
    fontFamily: FONTS.cocogoose,
    fontSize: scale(FONT_SIZE.xl2),
    lineHeight: scale(29.6),
    letterSpacing: 0.5,
    fontWeight: '500',
  },
  timerText: {
    fontFamily: FONTS.regular,
    fontSize: scale(FONT_SIZE.heading2),
    lineHeight: scale(48),
    letterSpacing: 0.5,
    fontWeight: '400',
  },
  timer: {
    fontFamily: FONTS.cocogoose,
    fontSize: scale(FONT_SIZE.timer),
    lineHeight: scale(132.98),
    letterSpacing: 0,
    fontWeight: '700',
  },
  dispensingText: {
    fontFamily: FONTS.cocogooseLight,
    fontSize: scale(FONT_SIZE.userTitle),
    lineHeight: scale(96),
    letterSpacing: 0,
    fontWeight: '300',
  },
  congratulationsText: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.congrats),
    lineHeight: scale(100),
    letterSpacing: 0,
  },
  racquetTitle: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.f45),
    lineHeight: scale(48),
    fontWeight: '500',
  },
  racquetType: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.f30),
    lineHeight: scale(48),
    fontWeight: '700',
  },
  tryText: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.md),
    fontWeight: '700',
    letterSpacing: 1,
  },
  gripSize: {
    fontFamily: FONTS.light,
    fontSize: scale(FONT_SIZE.subTitle),
    lineHeight: scale(30),
    fontWeight: '500',
  },
  toggleTab: {
    fontFamily: FONTS.bold,
    fontSize: scale(FONT_SIZE.tab),
    lineHeight: scale(20),
    fontWeight: '700',
  },
  appliedText: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.f25),
    fontWeight: '400',
  },
  appliedText2: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.f28),
    fontWeight: '700',
  },
  offText: {
    fontFamily: FONTS.cocogooseMedium,
    fontSize: scale(FONT_SIZE.f28),
    fontWeight: '500',
  },
  offText2: {
    fontFamily: FONTS.cocogooseLight,
    fontSize: scale(FONT_SIZE.f28),
    fontWeight: '400',
  },
});

export default Typography;
