/* eslint-disable @typescript-eslint/no-require-imports */
import {AnimationObject} from 'lottie-react-native';

export interface AnimationAssets {
  background: AnimationObject;
  buttonPill: AnimationObject;
  countDown: AnimationObject;
  upButton: AnimationObject;
  downButton: AnimationObject;
  hand: AnimationObject;
  buttonPill2: AnimationObject;
  buttonPill3: AnimationObject;
  buttonPill4: AnimationObject;
  ballBlasting: AnimationObject;
  RacquetAnimation: AnimationObject;
  RacquetAnimationGreen: AnimationObject;
  plusIconAnimation: AnimationObject;
}

// Using unknown as intermediate type to avoid direct casting
const animationSources = {
  background: require('../assest/animation/background.json') as unknown,
  countDown: require('../assest/animation/Countdown.json') as unknown,
  upButton: require('../assest/animation/AE-UP.json') as unknown,
  downButton: require('../assest/animation/AE-DOWN.json') as unknown,
  hand: require('../assest/animation/AE-HandGesture.json') as unknown,
  buttonPill:
    require('../assest/animation/PillButtons/AE-PillButton-594x104.json') as unknown,
  buttonPill2:
    require('../assest/animation/PillButtons/AE-PillButton-520x172.json') as unknown,
  buttonPill3:
    require('../assest/animation/PillButtons/PillButton-321x54.json') as unknown,
  buttonPill4:
    require('../assest/animation/PillButtons/PillButton-489x86.json') as unknown,
  ballBlastting:
    require('../assest/animation/AE-Ball-DunlopATP.json') as unknown,
  RacquetAnimation:
    require('../assest/animation/AE-Racquet-UpDownAnimation.json') as unknown,
  RacquetAnimationGreen:
    require('../assest/animation/AE-Racquet-UpDownAnimationGreen.json') as unknown,
  plusIconAnimation: require('../assest/animation/AE-PlusSign.json') as unknown,
};

// Cast to the proper types after loading
const animation: AnimationAssets = {
  background: animationSources.background as AnimationObject,
  countDown: animationSources.countDown as AnimationObject,
  upButton: animationSources.upButton as AnimationObject,
  downButton: animationSources.downButton as AnimationObject,
  hand: animationSources.hand as AnimationObject,
  buttonPill: animationSources.buttonPill as AnimationObject,
  buttonPill2: animationSources.buttonPill2 as AnimationObject,
  buttonPill3: animationSources.buttonPill3 as AnimationObject,
  buttonPill4: animationSources.buttonPill4 as AnimationObject,
  ballBlasting: animationSources.ballBlastting as AnimationObject,
  RacquetAnimation: animationSources.RacquetAnimation as AnimationObject,
  RacquetAnimationGreen:
    animationSources.RacquetAnimationGreen as AnimationObject,
  plusIconAnimation: animationSources.plusIconAnimation as AnimationObject,
};

export default animation;
