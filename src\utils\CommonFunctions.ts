/**
 * @fileoverview Common utility functions for navigation and general app functionality
 * @module Utils/CommonFunctions
 * @category Utilities
 */

import {
  CommonActions,
  NavigationContainerRefContext,
} from '@react-navigation/native';

/**
 * Dynamically resets the navigation stack
 *
 * @param {object} navigation - The navigation object
 * @param {number} keepCount - Number of screens to keep
 * @param {'start' | 'end'} from - Whether to keep screens from the start or end of the stack
 */
export const dynamicResetStack = (
  navigation,
  keepCount = 1,
  from = 'start',
) => {
  const state = navigation.getState();

  if (!state || !state.routes || state.routes.length === 0) return;

  let routesToKeep = [];

  if (from === 'start') {
    routesToKeep = state.routes.slice(0, keepCount);
  } else if (from === 'end') {
    routesToKeep = state.routes.slice(-keepCount);
  } else {
    throw new Error("Parameter 'from' must be either 'start' or 'end'");
  }

  const newRoutes = routesToKeep.map(route => ({
    name: route.name,
    params: route.params,
  }));

  console.log('newRoutes', state.routes, routesToKeep, keepCount, newRoutes);
  navigation.dispatch(
    CommonActions.reset({
      index: newRoutes.length - 1,
      routes: newRoutes,
    }),
  );
};

export const getBrandName = (name: string) => {
  switch (name) {
    case 'dunlop':
      return 'dunlop-full';
    case 'babolat':
      return 'babolat-full';
    case 'yonex':
      return 'yonex-full';
    case 'head':
      return 'head-full';
    case 'wilson':
      return 'wilson-full';
    case 'technifibre':
      return 'technifibre-full';
    case 'penn':
      return 'penn-full';
    case 'adidas':
      return 'adidas-full';
    case 'nox':
      return 'nox-full';
    case 'bullaPadel':
      return 'bullaPadel-full';

    default:
      return 'dunlop-full';
  }
};
