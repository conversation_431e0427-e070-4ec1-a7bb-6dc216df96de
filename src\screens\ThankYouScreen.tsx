import React, {useEffect} from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useLanguageStore} from '@/store/languageStore';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';

const ThankYouScreen = () => {
  const {t} = useLanguageStore();
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const isFocused = useIsFocused();

  useEffect(() => {
    const timeOut = setTimeout(() => {
      if (isFocused) {
        navigation.navigate('Home');
      }
    }, 10000);
    return () => {
      clearTimeout(timeOut);
    };
  }, [isFocused, navigation]);

  return (
    <GradientBackground>
      <TouchableOpacity
        style={styles.container}
        activeOpacity={0.8}
        onPress={() => {
          navigation.navigate('Home');
        }}>
        <Typography
          variant="dispensingText"
          color={KioskConfig.theme.colors.text.highlight}
          align="center">
          {t('common.thankYou')}
        </Typography>
      </TouchableOpacity>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timer: {
    position: 'absolute',
    bottom: 260,
  },
});
export default ThankYouScreen;
