# Node dependencies
node_modules

# Android build files
android/app/build/
android/build/
android/.gradle/
android/.cxx/
*.apk
*.aab
android/app/release/
android/app/debug/
android/captures/
android/local.properties

# iOS build files
ios/build/
ios/Pods/
*.ipa
*.xcarchive
*.xcuserstate
xcuserdata/
DerivedData/
ios/Podfile.lock
ios/.xcode.env.local

# Binary files
*.so
*.dex

# Lock files
yarn.lock
package-lock.json

# Environment
.env
.env.*
!.env.example

# React Native specific
.expo/
.expo-shared/
*.jsbundle
*.hprof
.cxx/
index.android.bundle
index.ios.bundle
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React Native Reanimated
**/generated/source/codegen/**
**/react-native-reanimated.d.ts
**/react-native-reanimated-generated.d.ts
**/reanimated-generated.d.ts
**/.cxx/**/rnreanimated_autolinked_build/
**/prefab/**/react-native-reanimated/

# Misc
.DS_Store
*.log
.watchman-cookie-*
build/
dist/
coverage/
.idea/
.vscode/
*.swp
*.swo
.tmp/
tmp/
vendor/
vendor/bundle/
