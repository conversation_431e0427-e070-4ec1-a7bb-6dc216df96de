import React, {useEffect, useState} from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, View, Animated, Dimensions} from 'react-native';
import BrandedBallsCategory from '@/components/BrandedBallsCatogory';
import images from '@/config/images';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {scale, verticalScale} from '@/utils/scale';

const CongratulationsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const [fadeAnim] = useState(new Animated.Value(0));
  const isFocused = useIsFocused();

  useEffect(() => {
    const timeOut = setTimeout(() => {
      if (isFocused) {
        navigation.navigate('CheckOutOrReturnScreen', {
          from: 'ball',
          item: {
            title: '',
            description: '',
            image: '',
            handleImage: 0,
          },
          selectedOption: '',
        });
      }
    }, 3000);
    return () => {
      clearTimeout(timeOut);
    };
  }, [isFocused, navigation]);

  return (
    <GradientBackground>
      <View style={StyleSheet.absoluteFillObject}>
        <View style={styles.congratsOverlay}>
          <View style={styles.textContainer}>
            <Typography
              variant="congratulationsText"
              align="center"
              color={KioskConfig.theme.colors.white}>
              Congratulations!
            </Typography>
          </View>
        </View>
      </View>
      <View style={styles.container}>
        <BrandedBallsCategory
          image={images.aoBall}
          ballImage={images.atpBall}
          showAddIcon={false}
          style={styles.image}
          ballImageStyle={styles.ballImage}
          ballImageContainerStyle={styles.ballImageContainer}
        />
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: scale(200),
  },
  congratsOverlay: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0,0.65)',
    height: Dimensions.get('screen').height,
    width: Dimensions.get('screen').width,
    position: 'absolute',
    left: -30,
    top: -110,
    zIndex: 10,
    bottom: 0,
  },
  textContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: scale(70),
  },

  image: {
    width: scale(298),
    height: verticalScale(900),
  },
  ballImage: {
    width: scale(283),
    height: verticalScale(280),
  },
  ballImageContainer: {
    position: 'absolute',
    right: '-50%',
    bottom: '-8%',
  },
});

export default CongratulationsScreen;
