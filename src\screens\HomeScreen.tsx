import React, {useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import GradientBackground from '../components/GradientBackground';
import {scale, verticalScale} from '@/utils/scale';
import {Carousel, CarouselItemType} from '../components/carousel';
import {SharedValue} from 'react-native-reanimated';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import CustomButton from '@/components/CustomButton';
import {useLanguageStore} from '@/store/languageStore';

// Simplified interface with just screen name
interface NavigationButton {
  id: string;
  text: string;
  text2?: string;
  screenName: keyof MainStackParamList | null;
}

const HomeScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();

  // Simplified array with just screen names and translation keys
  const buttons: NavigationButton[] = [
    {
      id: '1',
      text: 'home.rentDemoBuy',
      screenName: 'RentDemoBuy',
    },
    {
      id: '2',
      text: 'home.reserved',
      text2: 'home.reservedEquipment',
      screenName: 'ReservedOrReturn',
    },
    {
      id: '3',
      text: 'home.stringing',
      screenName: 'StringingGripping', // No navigation yet
    },
    {
      id: '4',
      text: 'home.request',
      text2: 'home.equipmentMaintenance',
      screenName: null, // No navigation yet
    },
  ];

  // Handle navigation based on screen name
  const handleNavigation = useCallback(
    (screenName: keyof MainStackParamList | null) => {
      if (screenName) {
        // Use type assertion to handle the navigation properly
        // Using 'any' here because TypeScript has difficulty with the complex navigation types
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        navigation.navigate(screenName as any);
      }
    },
    [navigation],
  );

  // Render carousel item
  const renderCarouselItem = useCallback(
    (
      item: CarouselItemType,
      _index: number,
      _animatedValue: SharedValue<number>,
    ) => {
      // Cast the item to our NavigationButton type
      const navButton = item as unknown as NavigationButton;

      return (
        <CustomButton
          text={t(navButton.text)}
          text2={navButton.text2 ? t(navButton.text2) : undefined}
          onPress={() => handleNavigation(navButton.screenName)}
          style={styles.firstButton}
        />
      );
    },
    [handleNavigation, t],
  );

  // Handle carousel item press
  const handleCarouselItemPress = useCallback(
    (_item: CarouselItemType, _index: number) => {
      // This function is required by the Carousel component but we're not using it
      // since we handle navigation in the renderCarouselItem function
    },
    [],
  );

  return (
    <GradientBackground backButton={false}>
      <View style={styles.contentContainer}>
        <Carousel
          data={buttons}
          renderItem={renderCarouselItem}
          itemWidth={scale(300)}
          itemGap={scale(55)}
          showNavigation={false}
          onItemPress={handleCarouselItemPress}
        />
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    marginLeft: scale(125),
    marginBottom: scale(220),
    // backgroundColor: 'red',
  },

  firstButton: {
    minWidth: scale(520),
    height: verticalScale(172),
    // flexWrap: 'wrap',
    // alignContent: 'center',
  },
});

export default HomeScreen;
