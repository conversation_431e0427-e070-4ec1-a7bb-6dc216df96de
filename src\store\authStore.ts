/**
 * @fileoverview Auth Store - Zustand store for managing user authentication state
 * @module Store/AuthStore
 * @category State Management
 */

import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {zustandStorage} from '../storage/mmkvStorage';

export interface User {
  id: string;
  name: string;
  email?: string;
  display_name?: string;
  status?: number;
  user_type?: string;
  is_chat_on?: boolean;
  is_notification_on?: boolean;
  image?: string;
  accessToken?: string;
  refreshToken?: string;
}

interface AuthState {
  user: User | null;
  login: (user: User) => void;
  logout: () => void;
}

const initialState = {
  user: null,
};

export const useAuthStore = create<AuthState>()(
  persist(
    set => ({
      user: initialState.user,
      login: user => set({user}),
      logout: () => set(initialState),
    }),
    {
      name: 'auth-store',
      storage: createJSONStorage(() => zustandStorage),
    },
  ),
);
