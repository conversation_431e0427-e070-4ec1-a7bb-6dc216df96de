import React from 'react';
import {View, StyleSheet} from 'react-native';
import GradientBackground from '../../components/GradientBackground';
import KioskConfig from '../../config/KioskConfig';
import CustomButton from '../../components/CustomButton';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '../../navigation';
import {useReservation} from '../../context/ReservationContext';
import {scale, verticalScale} from '@/utils/scale';
import Typography from '@/components/Typography';
import {tokenStorage} from '@/services/api';
import {useAuthStore} from '@/store/authStore';

const ReservedOrReturnScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {setFlowType} = useReservation();
  const accessToken = tokenStorage.getString('accessToken');
  const {user} = useAuthStore();

  const redirecFunction = () => {
    console.log('accessToken', accessToken, user);

    if (user?.accessToken) {
      navigation.navigate('GreetingScreen');
    } else {
      navigation.navigate('OTPorQRScreen');
    }
  };

  const handleCheckout = () => {
    setFlowType('CHECKOUT');
    redirecFunction();
  };

  const handleReturn = () => {
    setFlowType('RETURN');
    redirecFunction();
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <Typography
            style={styles.title}
            variant="heading2"
            color={KioskConfig.theme.colors.white}>
            Select an Option
          </Typography>
          <View style={styles.buttonContainer}>
            <CustomButton
              style={styles.button}
              text="Checkout"
              onPress={handleCheckout}
            />
            <CustomButton
              style={styles.button}
              text="Return"
              onPress={handleReturn}
            />
          </View>
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: verticalScale(100),
  },
  contentContainer: {
    width: '80%',
    alignItems: 'center',
  },
  title: {
    marginBottom: scale(30),
    textAlign: 'center',
    fontWeight: '400',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: scale(90),
  },
  button: {
    minWidth: scale(520),
    height: verticalScale(172),
  },
});

export default ReservedOrReturnScreen;
