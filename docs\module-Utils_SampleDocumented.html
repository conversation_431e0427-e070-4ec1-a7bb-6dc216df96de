<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Kiosk App - React Native Documentation Utils/SampleDocumented</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">Kiosk Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                 
                    
                        <a
                            class="link user-link "
                            href="#components"
                        >
                            Components
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#screens"
                        >
                            Screens
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#utils"
                        >
                            Utils
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#services"
                        >
                            Services
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#hooks"
                        >
                            Hooks
                        </a>
                    
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Modules</h3><ul><li><a href="module-Utils_SampleDocumented.html">Utils/SampleDocumented</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Module</p>
                    <h1>Utils/SampleDocumented</h1>
                </header>
                




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description"><p>Sample documented utility functions for JSDoc testing</p></div>
        

        
            














<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="sampleDocumented.js.html" class="button">View Source</a>
            <span>
                <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line1">line 1</a>
            </span>
        </p>
    
</dl>






















        
    
    </div>
    
    

    

    

    

    

    

    
        <div class='vertical-section'>
            <h1>Members</h1>
            <div class="members">
            
                <div class="member">

    <span class="method-parameter is-pulled-right">
        <label>Type:</label>
        
<code class="param-type">Object</code>


    </span>

<h4 class="name" id=".CONFIG">
    <a class="href-link" href="#.CONFIG">#</a>
    
        
            <span class='tag'>static</span>
        
            <span class='tag'>constant</span>
        
    
    <span class="code-name">
        CONFIG
    </span>
    
</h4>




<div class="description">
    <p>Configuration object for the sample module</p>
</div>





    <h5 class="subsection-title">Properties:</h5>

    
<div class="table-container">
    <table class="props table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        
            
<tr class="deep-level-0">
    
        <td class="name"><code>version</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    

    

    <td class="description last"><p>The module version</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>debug</code></td>
    

    <td class="type">
    
        
<code class="param-type">boolean</code>


    
    </td>

    

    

    <td class="description last"><p>Whether debug mode is enabled</p></td>
</tr>

        
        </tbody>
    </table>
</div>



<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="sampleDocumented.js.html" class="button">View Source</a>
            <span>
                <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line41">line 41</a>
            </span>
        </p>
    
</dl>





</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Methods</h1>
            <div class="members">
            
                <div class="member">


    
    <h4 class="name" id="~add">
        <a class="href-link" href="#~add">#</a>
        
            
                <span class='tag'>inner</span>
            
        
        <span class="code-name">
            
                add<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {number}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Calculates the sum of two numbers</p>
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>a</code></td>
  

  <td class="type">
  
      
<code class="param-type">number</code>


  
  </td>

  

  

  <td class="description last"><p>The first number</p></td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>b</code></td>
  

  <td class="type">
  
      
<code class="param-type">number</code>


  
  </td>

  

  

  <td class="description last"><p>The second number</p></td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="sampleDocumented.js.html" class="button">View Source</a>
            <span>
                <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line7">line 7</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'><p>The sum of a and b</p></div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<code class="param-type">number</code>


    </div>
    
</div>

                
        </div>
    </div>




    <h5>Example</h5>
    
    
        <pre class="prettyprint"><code>const result = add(5, 3); // returns 8</code></pre>
    


</div>
            
                <div class="member">


    
    <h4 class="name" id="~createUser">
        <a class="href-link" href="#~createUser">#</a>
        
            
                <span class='tag'>inner</span>
            
        
        <span class="code-name">
            
                createUser<span class="signature">(name, email)</span><span class="type-signature"> &rarr; {User}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Creates a new user object</p>
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>name</code></td>
  

  <td class="type">
  
      
<code class="param-type">string</code>


  
  </td>

  

  

  <td class="description last"><p>The user's name</p></td>
</tr>


        

            
<tr class="deep-level-0">
  
      <td class="name"><code>email</code></td>
  

  <td class="type">
  
      
<code class="param-type">string</code>


  
  </td>

  

  

  <td class="description last"><p>The user's email</p></td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="sampleDocumented.js.html" class="button">View Source</a>
            <span>
                <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line54">line 54</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'><p>The created user object</p></div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<code class="param-type">User</code>


    </div>
    
</div>

                
        </div>
    </div>




    <h5>Example</h5>
    
    
        <pre class="prettyprint"><code>const user = createUser('John Doe', '<EMAIL>');</code></pre>
    


</div>
            
                <div class="member">


    
    <h4 class="name" id="~formatString">
        <a class="href-link" href="#~formatString">#</a>
        
            
                <span class='tag'>inner</span>
            
        
        <span class="code-name">
            
                formatString<span class="signature">(str)</span><span class="type-signature"> &rarr; {string}</span>
            
        </span>
    </h4>
    

    
    
    <div class="description">
        <p>Formats a string with proper capitalization</p>
    </div>
    









    <h5>Parameters:</h5>
    
<div class="table-container">
    <table class="params table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            

            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        

            
<tr class="deep-level-0">
  
      <td class="name"><code>str</code></td>
  

  <td class="type">
  
      
<code class="param-type">string</code>


  
  </td>

  

  

  <td class="description last"><p>The string to format</p></td>
</tr>


        
        </tbody>
    </table>
</div>





<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="sampleDocumented.js.html" class="button">View Source</a>
            <span>
                <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line20">line 20</a>
            </span>
        </p>
    
</dl>


















    <div class='columns method-parameter'>
        <div class="column is-2"><label>Returns:</label></div>
        <div class="column is-10">
            
                    

<div class="columns">
    
    <div class='param-desc column is-7'><p>The formatted string</p></div>
    
    
    <div class='column is-5 has-text-left'>
        <label>Type: </label>
        
<code class="param-type">string</code>


    </div>
    
</div>

                
        </div>
    </div>




    <h5>Example</h5>
    
    
        <pre class="prettyprint"><code>const formatted = formatString('hello world'); // returns 'Hello World'</code></pre>
    


</div>
            
            </div>
        </div>
    

    
        <div class='vertical-section'>
            <h1>Type Definitions</h1>
            <div class="members">
            
                <div class="member">

    <span class="method-parameter is-pulled-right">
        <label>Type:</label>
        
<code class="param-type">Object</code>


    </span>

<h4 class="name" id="~User">
    <a class="href-link" href="#~User">#</a>
    
    <span class="code-name">
        User
    </span>
    
</h4>








    <h5 class="subsection-title">Properties:</h5>

    
<div class="table-container">
    <table class="props table">
        <thead>
        <tr>
            
            <th>Name</th>
            

            <th>Type</th>

            
            <th>Attributes</th>
            

            
            <th>Default</th>
            

            <th class="last">Description</th>
        </tr>
        </thead>

        <tbody>
        
            
<tr class="deep-level-0">
    
        <td class="name"><code>id</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        

        
        </td>
    

    
        <td class="default">
        
        </td>
    

    <td class="description last"><p>User unique identifier</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>name</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        

        
        </td>
    

    
        <td class="default">
        
        </td>
    

    <td class="description last"><p>User display name</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>email</code></td>
    

    <td class="type">
    
        
<code class="param-type">string</code>


    
    </td>

    
        <td class="attributes">
        

        
        </td>
    

    
        <td class="default">
        
        </td>
    

    <td class="description last"><p>User email address</p></td>
</tr>

        
            
<tr class="deep-level-0">
    
        <td class="name"><code>active</code></td>
    

    <td class="type">
    
        
<code class="param-type">boolean</code>


    
    </td>

    
        <td class="attributes">
        
            &lt;optional><br>
        

        
        </td>
    

    
        <td class="default">
        
            true
        
        </td>
    

    <td class="description last"><p>Whether the user is active</p></td>
</tr>

        
        </tbody>
    </table>
</div>



<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    
    

    

    

    
        <p class="tag-source">
            <a href="sampleDocumented.js.html" class="button">View Source</a>
            <span>
                <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line46">line 46</a>
            </span>
        </p>
    
</dl>





</div>
            
            </div>
        </div>
    

    
</article>

</section>




            </div>
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>