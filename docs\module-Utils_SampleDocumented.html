<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Module: Utils/SampleDocumented</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Module: Utils/SampleDocumented</h1>

    




<section>

<header>
    
        
            
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description"><p>Sample documented utility functions for JSDoc testing</p></div>
        

        
            















<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line1">line 1</a>
    </li></ul></dd>
    

    

    

    
</dl>




















        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        
            
<h4 class="name" id=".CONFIG"><span class="type-signature">(static, constant) </span>CONFIG<span class="type-signature"> :Object</span></h4>




<div class="description">
    <p>Configuration object for the sample module</p>
</div>



    <h5>Type:</h5>
    <ul>
        <li>
            
<span class="param-type">Object</span>


        </li>
    </ul>





    <h5 class="subsection-title">Properties:</h5>

    

<table class="props">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>version</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last"><p>The module version</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debug</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            

            

            <td class="description last"><p>Whether debug mode is enabled</p></td>
        </tr>

    
    </tbody>
</table>




<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line41">line 41</a>
    </li></ul></dd>
    

    

    

    
</dl>






        
    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    
    <h4 class="name" id="~add"><span class="type-signature">(inner) </span>add<span class="signature">(a, b)</span><span class="type-signature"> &rarr; {number}</span></h4>
    

    



<div class="description">
    <p>Calculates the sum of two numbers</p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>a</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The first number</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>b</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            

            <td class="description last"><p>The second number</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line7">line 7</a>
    </li></ul></dd>
    

    

    

    
</dl>















<h5>Returns:</h5>

        
<div class="param-desc">
    <p>The sum of a and b</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">number</span>


    </dd>
</dl>

    




    <h5>Example</h5>
    
    <pre class="prettyprint"><code>const result = add(5, 3); // returns 8</code></pre>



        
            

    

    
    <h4 class="name" id="~createUser"><span class="type-signature">(inner) </span>createUser<span class="signature">(name, email)</span><span class="type-signature"> &rarr; {User}</span></h4>
    

    



<div class="description">
    <p>Creates a new user object</p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last"><p>The user's name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>email</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last"><p>The user's email</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line54">line 54</a>
    </li></ul></dd>
    

    

    

    
</dl>















<h5>Returns:</h5>

        
<div class="param-desc">
    <p>The created user object</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">User</span>


    </dd>
</dl>

    




    <h5>Example</h5>
    
    <pre class="prettyprint"><code>const user = createUser('John Doe', '<EMAIL>');</code></pre>



        
            

    

    
    <h4 class="name" id="~formatString"><span class="type-signature">(inner) </span>formatString<span class="signature">(str)</span><span class="type-signature"> &rarr; {string}</span></h4>
    

    



<div class="description">
    <p>Formats a string with proper capitalization</p>
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>str</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            

            <td class="description last"><p>The string to format</p></td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line20">line 20</a>
    </li></ul></dd>
    

    

    

    
</dl>















<h5>Returns:</h5>

        
<div class="param-desc">
    <p>The formatted string</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">string</span>


    </dd>
</dl>

    




    <h5>Example</h5>
    
    <pre class="prettyprint"><code>const formatted = formatString('hello world'); // returns 'Hello World'</code></pre>



        
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        
                
<h4 class="name" id="~User">User</h4>






    <h5>Type:</h5>
    <ul>
        <li>
            
<span class="param-type">Object</span>


        </li>
    </ul>





    <h5 class="subsection-title">Properties:</h5>

    

<table class="props">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>id</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>User unique identifier</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>User display name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>email</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>User email address</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>active</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last"><p>Whether the user is active</p></td>
        </tr>

    
    </tbody>
</table>




<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="sampleDocumented.js.html">sampleDocumented.js</a>, <a href="sampleDocumented.js.html#line46">line 46</a>
    </li></ul></dd>
    

    

    

    
</dl>






            
    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-Utils_SampleDocumented.html">Utils/SampleDocumented</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 4.0.4</a> on Fri Jul 04 2025 15:19:28 GMT+0530 (India Standard Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>