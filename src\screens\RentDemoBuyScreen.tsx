import React, {useCallback} from 'react';
import Grad<PERSON>Background from '../components/GradientBackground';
import RentingDemo from '../components/RentingDemo';
import {Dimensions, StyleSheet} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import images from '@/config/images';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';
import {actionButtons} from '@/utils/staticData';

const RentDemoBuyScreen = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();

  // Create a single navigation handler for all buttons
  const handleNavigation = useCallback(
    (screenName: string, params?: Record<string, unknown>) => {
      navigation.navigate(screenName as any, params);
    },
    [navigation],
  );

  return (
    <GradientBackground>
      <RentingDemo
        containerStyle={styles.container}
        actionButtons={actionButtons as any} // Type assertion to fix compatibility
        btnStyle={styles.btnStyle}
        activeImage={images.racquet}
        btnContainerStyle={{
          maxHeight: verticalScale(Dimensions.get('window').height * 0.5),
        }}
        imgContainerStyle={{top: -20, right: scale(100)}}
        title={t('rentDemoBuy.title')}
        onNavigate={handleNavigation} // Pass the navigation handler
      />
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    maxWidth: scale(1440),
    alignItems: 'center',
    flexDirection: 'row',
    margin: 'auto',
  },
  btnStyle: {
    width: scale(594),
    height: verticalScale(104),
    // paddingHorizontal: scale(24),
    // justifyContent: 'center',
    // alignItems: 'flex-start',
  },
});

export default RentDemoBuyScreen;
