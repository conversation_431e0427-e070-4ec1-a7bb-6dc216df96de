/**
 * @fileoverview Header component for the Kiosk application
 * @module Components/Header
 * @category Components
 */

import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import KioskConfig from '../config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../navigation';
import Icon from './Icon';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';

/**
 * Props interface for the Header component
 * @interface HeaderProps
 * @property {boolean} [showBackButton=false] - Whether to show the back button
 * @property {boolean} [showHomeButton=true] - Whether to show the home button
 * @property {boolean} [showCart=false] - Whether to show the cart icon
 * @property {number} [cartItemCount=0] - Number of items in the cart
 * @property {Function} [handleBackPress] - Custom back button press handler
 * @property {Function} [cartPress] - Cart button press handler
 */
interface HeaderProps {
  showBackButton?: boolean;
  showHomeButton?: boolean;
  showCart?: boolean;
  cartItemCount?: number;
  handleBackPress?: () => void;
  cartPress?: () => void;
}

type NavigationType = StackNavigationProp<RootStackParamList>;

/**
 * Header component for the Kiosk application
 *
 * Provides navigation controls including back button, home button, and cart icon.
 * Supports internationalization and responsive design.
 *
 * @component
 * @param {HeaderProps} props - The props for the Header component
 * @returns {JSX.Element} The rendered Header component
 *
 * @example
 * // Basic header with home button only
 * <Header />
 *
 * @example
 * // Header with back button and cart
 * <Header
 *   showBackButton={true}
 *   showCart={true}
 *   cartItemCount={3}
 *   handleBackPress={() => navigation.goBack()}
 *   cartPress={() => navigation.navigate('Cart')}
 * />
 *
 * @example
 * // Header without home button
 * <Header
 *   showHomeButton={false}
 *   showBackButton={true}
 * />
 */
const Header: React.FC<HeaderProps> = ({
  showCart = false,
  showBackButton = false,
  showHomeButton = true,
  cartItemCount = 0,
  handleBackPress,
  cartPress = () => {},
}) => {
  const {colors} = KioskConfig.theme;
  const navigation = useNavigation<NavigationType>();
  const {t} = useLanguageStore();

  // Get current date, time and temp
  const date = new Date();
  const formattedDate = date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });

  const formattedTime = date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });

  // Mock temperature data (in a real app, this would come from a weather API)
  const temperature = '56° F';

  const handleHomePress = () => {
    navigation.navigate('Welcome');
  };

  const onBackPress = () => {
    if (handleBackPress) {
      handleBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.main}>
      <View style={[styles.container]}>
        {showHomeButton && (
          <TouchableOpacity style={styles.homeButton} onPress={handleHomePress}>
            <Icon name="home" size={scale(40)} color={colors.text.secondary} />
            <Text style={[styles.buttonText, {color: colors.text.secondary}]}>
              {t('home.title').toUpperCase()}
            </Text>
          </TouchableOpacity>
        )}

        <View style={styles.centerContent}>
          <Text style={[styles.dateText, {color: colors.text.secondary}]}>
            {formattedDate}
          </Text>
          <Text style={[styles.timeText, {color: colors.text.secondary}]}>
            {formattedTime}
          </Text>
          <Text style={[styles.tempText, {color: colors.text.secondary}]}>
            ☀️ {temperature}
          </Text>
        </View>

        <View style={styles.rightContent}>
          {cartItemCount > 0 && showCart && (
            <View style={[styles.cartContainer]}>
              <View style={styles.cartIcon}>
                <Text style={styles.cartCount}>{cartItemCount}</Text>
              </View>
            </View>
          )}

          {showBackButton && (
            <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
              <Icon
                name="back"
                size={scale(40)}
                color={colors.text.secondary}
              />
              <Text style={[styles.buttonText, {color: colors.text.secondary}]}>
                {t('common.back').toUpperCase()}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
      {showCart && (
        <TouchableOpacity style={styles.cart} onPress={cartPress}>
          <Icon name="cart" size={scale(40)} color={colors.text.secondary} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  main: {
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    position: 'relative',
    zIndex: 3,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: 'transparent',
    position: 'relative',
    zIndex: 1,
  },
  homeButton: {
    alignItems: 'center',
  },
  homeIcon: {
    width: scale(46),
    height: verticalScale(46),
  },
  iconContainer: {
    marginRight: scale(5),
  },
  centerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  dateText: {
    fontSize: scale(23.98),
    marginRight: scale(69),
  },
  timeText: {
    fontSize: scale(26.65),
    marginRight: scale(69),
  },
  tempText: {
    fontSize: scale(26.65),
  },
  rightContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cartContainer: {
    position: 'absolute',
    right: 0,
    bottom: scale(-30),
  },
  cartIcon: {
    width: scale(17.55),
    height: scale(17.55),
    borderRadius: scale(20),
    backgroundColor: '#DFFC4F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartCount: {
    color: 'black',
    fontWeight: 'bold',
  },
  backButton: {
    alignItems: 'center',
  },
  cart: {marginTop: scale(20), zIndex: 10},
  buttonText: {
    fontSize: scale(14),
    fontWeight: 700,
    marginTop: scale(5),
  },
  backIcon: {
    width: scale(54),
    height: scale(54),
  },
});

export default Header;
