import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ArrowLeftProps {
  width?: number;
  height?: number;
  stroke?: string;
  strokeWidth?: number;
}

const ArrowLeft: React.FC<ArrowLeftProps> = ({
  width = 24,
  height = 24,
  stroke = 'black',
  strokeWidth = 2,
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none">
      <Path
        d="M15 18L9 12L15 6"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ArrowLeft;
