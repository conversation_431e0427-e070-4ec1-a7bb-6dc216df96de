import React from 'react';
import {View, StyleSheet} from 'react-native';
import Typography from '../Typography';
import images from '@/config/images';
import KioskConfig from '@/config/KioskConfig';
import FastImage from 'react-native-fast-image';
import {scale, verticalScale} from '@/utils/scale';

const data = [
  {
    title: 'Materials',
    description:
      'We use only the purest natural rubber. Then we add our secret recipe and blend to create the final compound.',
    image: 'path/to/materials.png',
  },
  {
    title: 'Cores',
    description:
      "We extrude and measure individual 'blanks'. All under the strictest conditions.",
    image: 'path/to/cores.png',
  },
  {
    title: 'Adhesive',
    description:
      "We use controlled pressure and temperature to mould 'blanks' into half shells. Then we add adhesive to the edge of each half shell.",
    image: 'path/to/adhesive.png',
  },
  {
    title: 'Cloth',
    description:
      'We bond the rubber core to an extremely durable cloth fabric, which consists of the finest natural wool blended with nylon. This gives the ball optimum visibility and consistent playing characteristics.',
    image: 'path/to/cloth.png',
  },
];

const BallManufacturingProcess: React.FC = () => {
  return (
    <View style={styles.container}>
      {data?.map((item, index) => (
        <View key={index} style={styles.card}>
          <FastImage
            source={images.ball}
            style={styles.image}
            resizeMode="contain"
          />
          <Typography variant="heading" style={styles.title}>
            {item.title}
          </Typography>
          <Typography variant="subtitle" style={styles.desc}>
            {item.description}
          </Typography>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  card: {
    // Slightly less than 1/3 to account for spacing
    marginBottom: scale(25),
    padding: scale(12),
    borderRadius: scale(8),
    width: '20%',
  },
  image: {
    width: scale(200),
    height: verticalScale(200),
    marginBottom: scale(100),
  },
  title: {
    color: KioskConfig.theme.colors.text.gold,
    marginBottom: scale(8),
    textTransform: 'uppercase',
  },
  desc: {
    color: KioskConfig.theme.colors.text.primary,
  },
});

export default BallManufacturingProcess;
