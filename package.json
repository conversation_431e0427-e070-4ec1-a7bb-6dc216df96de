{"name": "kiosk", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "fix-all": "npm run format && npm run lint:fix", "start": "react-native start", "test": "jest", "release": "cd ./android && ./gradlew assemblerelease", "build:android": "cd android && ./gradlew assembleRelease"}, "dependencies": {"@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@sentry/react-native": "^6.14.0", "@tanstack/react-query": "^5.76.1", "@twotalltotems/react-native-otp-input": "^1.3.11", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.9.0", "i18n-js": "^4.5.1", "jwt-decode": "^4.0.0", "lottie-ios": "^4.5.1", "lottie-react-native": "^7.2.2", "react": "19.0.0", "react-native": "0.79.1", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.25.0", "react-native-idle-timer": "^2.2.3", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^3.2.0", "react-native-otp-entry": "^1.8.4", "react-native-reanimated": "^3.17.5", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.13.0", "react-native-vision-camera": "^4.6.4", "zustand": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.6.3", "prettier": "^3.5.3", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}