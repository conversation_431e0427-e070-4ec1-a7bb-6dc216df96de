/**
 * Font configuration for the Kiosk app
 */

export enum FontFamily {
  HelveticaNeue = 'HelveticaNeueLTStd',
  Cocogoose = 'Cocogoose-Classic',
}

export enum FontWeight {
  Thin = 'Thin',
  ExtraLight = 'ExtraLight',
  Light = 'Light',
  Regular = 'Regular',
  Medium = 'Medium',
  Bold = 'Bold',
  ExtraBold = 'ExtraBold',
  Black = 'Black',
  Italic = 'Italic',
}

export type FontConfig = {
  fontFamily: string;
  fontWeight?:
  | 'normal'
  | 'bold'
  | '100'
  | '200'
  | '300'
  | '400'
  | '500'
  | '600'
  | '700'
  | '800'
  | '900';
};

/**
 * Font constants for direct use in styles
 */
export const FONTS = {
  // Helvetica Neue variants
  thin: `${FontFamily.HelveticaNeue}-Th`,
  light: `${FontFamily.HelveticaNeue}-Lt`,
  regular: `${FontFamily.HelveticaNeue}-Roman`,
  medium: `${FontFamily.HelveticaNeue}-Md`,
  bold: `${FontFamily.HelveticaNeue}-Bd`,
  black: `${FontFamily.HelveticaNeue}-Blk`,

  // Cocogoose variants
  cocogooseThin: `${FontFamily.Cocogoose}-Thin`,
  cocogooseThinItalic: `${FontFamily.Cocogoose}-ThinItalic`,
  cocogooseExtraLight: `${FontFamily.Cocogoose}-ExtraLight`,
  cocogooseExtraLightItalic: `${FontFamily.Cocogoose}-ExtraLightIt`,
  cocogooseLight: `${FontFamily.Cocogoose}-Light`,
  cocogooseLightItalic: `${FontFamily.Cocogoose}-LightItalic`,
  cocogoose: `${FontFamily.Cocogoose}-Regular`,
  cocogooseItalic: `${FontFamily.Cocogoose}-Italic`,
  cocogooseMedium: `${FontFamily.Cocogoose}-Medium`,
  cocogooseMediumItalic: `${FontFamily.Cocogoose}-MediumItalic`,
  cocogooseBold: `${FontFamily.Cocogoose}-Bold`,
  cocogooseBoldItalic: `${FontFamily.Cocogoose}-BoldItalic`,
  cocogooseExtraBold: `${FontFamily.Cocogoose}-ExtraBold`,
  cocogooseExtraBoldItalic: `${FontFamily.Cocogoose}-ExtraBoldIt`,
  cocogooseBlack: `${FontFamily.Cocogoose}-Black`,
  cocogooseBlackItalic: `${FontFamily.Cocogoose}-BlackItalic`,
};

/**
 * Get font configuration for Helvetica Neue
 */
export const getHelveticaNeue = (weight = FontWeight.Regular): FontConfig => {
  let fontFamily: string = FontFamily.HelveticaNeue;
  let fontWeight: FontConfig['fontWeight'] = 'normal';

  switch (weight) {
    case FontWeight.Thin:
      fontFamily = FONTS.thin;
      fontWeight = '100';
      break;
    case FontWeight.Light:
      fontFamily = FONTS.light;
      fontWeight = '300';
      break;
    case FontWeight.Medium:
      fontFamily = FONTS.medium;
      fontWeight = '500';
      break;
    case FontWeight.Bold:
      fontFamily = FONTS.bold;
      fontWeight = '700';
      break;
    case FontWeight.Black:
      fontFamily = FONTS.black;
      fontWeight = '900';
      break;
    default:
      fontFamily = FONTS.regular;
      fontWeight = '400';
      break;
  }

  return { fontFamily, fontWeight };
};

/**
 * Get font configuration for Cocogoose
 */
export const getCocogoose = (
  weight = FontWeight.Regular,
  italic = false,
): FontConfig => {
  let fontFamily: string = FONTS.cocogoose;
  let fontWeight: FontConfig['fontWeight'] = 'normal';

  switch (weight) {
    case FontWeight.Thin:
      fontFamily = italic ? FONTS.cocogooseThinItalic : FONTS.cocogooseThin;
      fontWeight = '100';
      break;
    case FontWeight.ExtraLight:
      fontFamily = italic
        ? FONTS.cocogooseExtraLightItalic
        : FONTS.cocogooseExtraLight;
      fontWeight = '200';
      break;
    case FontWeight.Light:
      fontFamily = italic ? FONTS.cocogooseLightItalic : FONTS.cocogooseLight;
      fontWeight = '300';
      break;
    case FontWeight.Medium:
      fontFamily = italic ? FONTS.cocogooseMediumItalic : FONTS.cocogooseMedium;
      fontWeight = '500';
      break;
    case FontWeight.Bold:
      fontFamily = italic ? FONTS.cocogooseBoldItalic : FONTS.cocogooseBold;
      fontWeight = '700';
      break;
    case FontWeight.ExtraBold:
      fontFamily = italic
        ? FONTS.cocogooseExtraBoldItalic
        : FONTS.cocogooseExtraBold;
      fontWeight = '800';
      break;
    case FontWeight.Black:
      fontFamily = italic ? FONTS.cocogooseBlackItalic : FONTS.cocogooseBlack;
      fontWeight = '900';
      break;
    case FontWeight.Italic:
      fontFamily = FONTS.cocogooseItalic;
      fontWeight = '400';
      break;
    default:
      fontFamily = italic ? FONTS.cocogooseItalic : FONTS.cocogoose;
      fontWeight = '400';
      break;
  }

  return { fontFamily, fontWeight };
};
