/**
 * @fileoverview Language Store - Zustand store for managing application language and translations
 * @module Store/LanguageStore
 * @category State Management
 */

import {create} from 'zustand';
import {persist, createJSONStorage} from 'zustand/middleware';
import {zustandStorage} from '../storage/mmkvStorage';
import i18n, {localFiles} from '../utils/i18n';
import api from '../services/api';
import {Alert} from 'react-native';

/**
 * Supported languages in the application.
 * @typedef {'en' | 'es'} SupportedLanguage
 */
export type SupportedLanguage = 'en' | 'es';

/**
 * Interface for the language state management.
 * @interface LanguageState
 * @property {SupportedLanguage} currentLanguage - Currently selected language
 * @property {Function} setLanguage - Function to change the current language
 * @property {Function} t - Translation function
 * @property {boolean} initialized - Whether the language store has been initialized
 * @property {number} translationVersion - Version number for translation updates
 * @property {Function} updateTranslations - Function to update translations from API
 */
interface LanguageState {
  currentLanguage: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  t: (key: string, options?: object) => string;
  initialized: boolean;
  translationVersion: number;
  updateTranslations: (lang: SupportedLanguage) => Promise<void>;
}

// Create the language store
export const useLanguageStore = create<LanguageState>()(
  persist(
    (set, get) => ({
      currentLanguage: 'en',
      initialized: false,
      translationVersion: 0,

      setLanguage: (language: SupportedLanguage) => {
        i18n.locale = language;
        set({currentLanguage: language});
        // Fetch translations when language changes
        get().updateTranslations(language);
      },

      t: (key: string, options?: object) => {
        return i18n.t(key, options);
      },

      updateTranslations: async (lang: SupportedLanguage) => {
        try {
          const response = await api.get(
            `/app-configuration/kiosk?language=${lang}`,
          );
          console.log(`to fetch translations for ${lang}:`, response);
          const remote = response.data?.data?.json_data || {};
          i18n.store({[lang]: remote});
          bumpVersion();
        } catch (error) {
          console.error(`Failed to fetch translations for ${lang}:`, error);
          Alert.alert(
            'Error',
            'Could not fetch language from server. Using local fallback.',
          );
          i18n.store({[lang]: localFiles[lang]});
          bumpVersion();
        }
      },
    }),
    {
      name: 'language-store',
      storage: createJSONStorage(() => zustandStorage),
      partialize: state => ({
        currentLanguage: state.currentLanguage,
        initialized: true,
        translationVersion: state.translationVersion,
      }),
      onRehydrateStorage: () => state => {
        if (state) {
          i18n.locale = state.currentLanguage;
        }
      },
    },
  ),
);

// Helper function to bump the translation version
const bumpVersion = () => {
  useLanguageStore.setState(state => ({
    translationVersion: state.translationVersion + 1,
  }));
};
