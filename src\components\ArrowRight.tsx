import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ArrowRightProps {
  width?: number;
  height?: number;
  stroke?: string;
  strokeWidth?: number;
}

const ArrowRight: React.FC<ArrowRightProps> = ({
  width = 24,
  height = 24,
  stroke = 'black',
  strokeWidth = 2,
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none">
      <Path
        d="M9 6L15 12L9 18"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default ArrowRight;
