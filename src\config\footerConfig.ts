/**
 * @fileoverview Footer Configuration - Configuration for footer buttons in the kiosk application
 * @module Config/FooterConfig
 * @category Configuration
 */

/**
 * Interface for footer button configuration
 */
export interface FooterButtonConfig {
  id: string;
  labelKey: string;
}

/**
 * Footer buttons configuration array
 * Simple array of objects with just id and translation key
 * All handling logic is implemented in the Footer component based on id
 */
export const footerButtons: FooterButtonConfig[] = [
  {
    id: 'accessibility',
    labelKey: 'common.accessibility',
  },
  {
    id: 'faqs',
    labelKey: 'common.faq',
  },
  {
    id: 'languages',
    labelKey: 'common.language',
  },
];

export default footerButtons;
