import React, {memo, useState} from 'react';
import {
  ImageSourcePropType,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import images from '@/config/images';
import Typography from '../Typography';
import KioskConfig from '@/config/KioskConfig';
import Icon from '../Icon';
import {scale, verticalScale} from '@/utils/scale';
import FastImage, {ImageStyle} from 'react-native-fast-image';
import LottieView from 'lottie-react-native';
import animation from '@/config/animation';
import {useLanguageStore} from '@/store/languageStore';

type PadelData = {
  image: ImageSourcePropType;
  title: string;
  type: string;
  size: {width: number; height: number};
};
const BrandedPadelCategory = ({
  data,
  style = {},
  addOnPress,
}: {
  data: PadelData;
  style?: StyleProp<ImageStyle>;
  addOnPress?: () => void;
}) => {
  const {t} = useLanguageStore();

  const [isAnimating, setIsAnimating] = useState(false);

  const handlePress = () => {
    if (!isAnimating) {
      setIsAnimating(true);
    }
  };

  const handleAnimationFinish = () => {
    setIsAnimating(false);
    if (addOnPress) {
      addOnPress();
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      style={styles.root}
      onPress={handlePress}>
      <FastImage
        source={data?.image || images.dunlopCx200}
        style={[
          {
            width: scale(data?.size?.width),
            height: verticalScale(data?.size?.height),
          },
          style as ImageStyle,
        ]}
        resizeMode="contain"
      />

      <View style={styles.addIcon}>
        {/* <Icon
          name="add"
          size={scale(78)}
          color={KioskConfig.theme.colors.Mindaro}
        /> */}
        <View style={styles.addIconContainer}>
          <LottieView
            source={animation.plusIconAnimation}
            autoPlay={isAnimating}
            loop={false}
            onAnimationFinish={handleAnimationFinish}
            style={styles.plusBtn}
          />
        </View>
        <Typography
          variant="tryText"
          align="center"
          color={KioskConfig.theme.colors.white}
          style={styles.tryText}>
          {t('common.tryOrBuy')}
        </Typography>
      </View>
      <View style={styles.titleContainer}>
        <Typography
          variant="racquetType"
          color={KioskConfig.theme.colors.white}
          numberOfLines={2}
          style={styles.title}>
          {t(data?.type)}
        </Typography>
        <Typography
          variant="racquetTitle"
          color={KioskConfig.theme.colors.white}
          numberOfLines={2}
          style={styles.title}>
          {t(data?.title)}
        </Typography>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  root: {
    justifyContent: 'center',
    alignItems: 'center',
    width: scale(536),
    height: verticalScale(750),
  },

  title: {
    textAlign: 'center',
  },
  addIcon: {
    position: 'absolute',
    top: 10,
    right: 0,
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center',
    width: scale(100),
  },
  titleContainer: {
    marginTop: verticalScale(10),
    gap: scale(10),
  },
  tryText: {
    marginTop: verticalScale(0),
  },
  addIconContainer: {
    width: scale(100),
    height: verticalScale(100),
    justifyContent: 'center',
    alignItems: 'center',
  },
  plusBtn: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default memo(BrandedPadelCategory);
