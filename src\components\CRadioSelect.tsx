// src/components/RadioSelect.tsx
import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  StyleProp,
} from 'react-native';
import Typography from './Typography';
import KioskConfig from '@/config/KioskConfig';
import {scale, verticalScale} from '@/utils/scale';

interface RadioSelectProps {
  label: string;
  selected: boolean;
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
}

const RadioSelect: React.FC<RadioSelectProps> = ({
  label,
  selected,
  onPress,
  containerStyle,
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={onPress}
      activeOpacity={0.7}>
      <View
        style={[
          styles.radioCircle,
          {
            borderColor: KioskConfig.theme.colors.white,
            backgroundColor: selected
              ? KioskConfig.theme.colors.Mindaro
              : 'transparent',
          },
        ]}
      />
      <Typography variant="radioLabel" color={KioskConfig.theme.colors.white}>
        {label}
      </Typography>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: scale(1),
  },
  radioCircle: {
    height: scale(34.72),
    width: scale(34.78),
    borderRadius: scale(35),
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: scale(10),
  },
  selectedInnerCircle: {
    height: verticalScale(24),
    width: scale(24),
    borderRadius: scale(24),
    borderWidth: 1,
  },
});

export default RadioSelect;
