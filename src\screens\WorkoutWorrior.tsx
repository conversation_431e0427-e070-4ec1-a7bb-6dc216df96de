import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import {ComfortableRacquetData, PowerRacquetData} from '@/config/staticData';
import {MainStackParamList} from '@/navigation';
import {useLanguageStore} from '@/store/languageStore';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';

export type workoutWarriorProps = {
  route: RouteProp<MainStackParamList, 'workoutWarrior'>;
};

const WorkoutWarrior = ({route}: workoutWarriorProps) => {
  const {from} = route.params || {};
  const {t} = useLanguageStore();
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        description={t('workoutWarrior.description')}
        btnText1={{
          text: t('workoutWarrior.btnText1_1'),
          text2: t('workoutWarrior.btnText1_2'),
        }}
        btnText2={{
          text: t('workoutWarrior.btnText2_1'),
          text2: '',
        }}
        onPress={() => {
          navigation.navigate('RacquetDetails', {
            from: from,
            racquetData: ComfortableRacquetData,
          });
        }}
        onPress2={() => {
          navigation.navigate('RacquetDetails', {
            from: from,
            racquetData: PowerRacquetData,
          });
        }}
      />
    </GradientBackground>
  );
};

export default WorkoutWarrior;
