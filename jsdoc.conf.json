{"source": {"include": ["./src/components", "./src/hooks", "./src/utils", "./src/screens", "./src/services", "./src/config", "./src/context", "./src/navigation", "./src/storage", "./src/store", "./src/translations", "./README.md"], "includePattern": "\\.(js|jsx|ts|tsx)$", "exclude": ["node_modules/", "android/", "ios/", "__tests__/", "docs/", "**/*.test.*", "**/*.spec.*"]}, "opts": {"destination": "./docs/", "recurse": true, "readme": "./README.md", "template": "node_modules/better-docs"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-babel"], "templates": {"cleverLinks": true, "monospaceLinks": true, "search": true, "hideGenerator": true, "better-docs": {"name": "Kiosk Documentation", "title": "Kiosk App - React Native Documentation", "hideGenerator": true, "trackingCode": "", "navigation": {"includeDate": false, "includeVersion": false}, "component": {"wrapper": "article"}, "navLinks": []}}, "babel": {"extensions": ["js", "jsx", "ts", "tsx"], "ignore": ["**/*.test.js", "**/*.test.ts", "**/*.test.tsx"], "babelrc": false, "presets": [["@babel/preset-env", {"targets": {"node": "current"}}], "@babel/preset-typescript", "@babel/preset-react"], "plugins": [["@babel/plugin-transform-class-properties", {"loose": true}], ["@babel/plugin-transform-private-methods", {"loose": true}], ["@babel/plugin-transform-private-property-in-object", {"loose": true}], "@babel/plugin-transform-object-rest-spread"]}}