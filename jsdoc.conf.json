{"source": {"include": ["./src/utils/sampleDocumented.js", "./src/components/Header.tsx", "./src/components/Footer.tsx", "./src/screens/HomeScreen.tsx", "./src/utils/scale.ts", "./src/services/api.ts", "./src/hooks/queries/useAuth.ts", "./README.md"], "includePattern": "\\.(js|jsx|ts|tsx)$", "exclude": ["node_modules/", "android/", "ios/", "__tests__/", "*.test.js", "*.test.ts", "*.test.tsx"]}, "opts": {"destination": "./docs/", "recurse": false, "readme": "./README.md", "template": "node_modules/better-docs"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-babel"], "templates": {"cleverLinks": true, "monospaceLinks": true, "search": true, "hideGenerator": true, "better-docs": {"name": "Kiosk Documentation", "title": "Kiosk App - React Native Documentation", "hideGenerator": true, "trackingCode": "", "css": "styles/style.css", "navigation": [], "navLinks": []}}, "babel": {"extensions": ["js", "jsx", "ts", "tsx"], "ignore": ["**/*.test.js", "**/*.test.ts", "**/*.test.tsx"], "babelrc": false, "presets": [["@babel/preset-env", {"targets": {"node": "current"}}], "@babel/preset-typescript", "@babel/preset-react"], "plugins": [["@babel/plugin-transform-class-properties", {"loose": true}], ["@babel/plugin-transform-private-methods", {"loose": true}], ["@babel/plugin-transform-private-property-in-object", {"loose": true}], "@babel/plugin-transform-object-rest-spread"]}}