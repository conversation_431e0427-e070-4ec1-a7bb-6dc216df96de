{"source": {"include": ["./src/utils/sampleDocumented.js", "./src/components/Header.tsx", "./src/components/Footer.tsx", "./src/screens/HomeScreen.tsx", "./src/utils/scale.ts", "./src/services/api.ts", "./src/hooks/queries/useAuth.ts", "./README.md"], "includePattern": "\\.(js|jsx|ts|tsx)$", "exclude": ["node_modules/", "android/", "ios/", "__tests__/", "*.test.js", "*.test.ts", "*.test.tsx"]}, "opts": {"destination": "./docs/", "recurse": false, "readme": "./README.md", "template": "node_modules/better-docs"}, "plugins": ["plugins/markdown", "node_modules/jsdoc-babel"], "templates": {"cleverLinks": true, "monospaceLinks": true, "search": true, "hideGenerator": true, "better-docs": {"name": "Kiosk Documentation", "title": "Kiosk App - React Native Documentation", "hideGenerator": true, "trackingCode": "", "css": "styles/style.css", "navigation": [{"label": "GitHub", "href": "https://github.com/your-repo/kiosk"}, {"label": "Documentation", "href": "/"}], "navLinks": [{"label": "Components", "href": "#components"}, {"label": "Screens", "href": "#screens"}, {"label": "Utils", "href": "#utils"}, {"label": "Services", "href": "#services"}, {"label": "<PERSON>s", "href": "#hooks"}]}}}