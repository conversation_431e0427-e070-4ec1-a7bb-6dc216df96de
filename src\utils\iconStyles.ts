import {TextStyle, Platform} from 'react-native';
import {parseIconsFromSelection, getIcomoonFontFamily} from './iconUtils';

// Import the selection.json conditionally
let icomoonSelection = {};
try {
  // Attempt to import the selection.json
  icomoonSelection = require('../assest/fonts/icomoon/selection.json');
  console.log('Icomoon selection loaded successfully');

  // Cast to any to avoid TypeScript errors
  const selectionWithTypes = icomoonSelection as any;
  if (
    selectionWithTypes &&
    selectionWithTypes.preferences &&
    selectionWithTypes.preferences.fontPref
  ) {
    console.log(
      'Font family from selection:',
      selectionWithTypes.preferences.fontPref.metadata?.fontFamily,
    );
  }
} catch (error) {
  console.warn(
    'Icomoon selection.json not found. Using fallback icons.',
    error,
  );
}

interface IconMap {
  [key: string]: string;
}

// Parse the icons from the selection.json
export const ICON_MAP: IconMap = Object.keys(icomoonSelection)?.length
  ? parseIconsFromSelection(icomoonSelection as any)
  : {
      // Fallback icons until selection.json is properly loaded
      home: '\uE900',
      search: '\uE901',
      user: '\uE902',
    };

// Get the font family from the selection.json
const fontFamilyName = Object.keys(icomoonSelection).length
  ? getIcomoonFontFamily(icomoonSelection as any)
  : Platform.OS === 'ios'
    ? 'Icomoon'
    : 'icomoon';

console.log('Using font family:', fontFamilyName);

export const iconStyle: TextStyle = {
  fontFamily: fontFamilyName,
  fontWeight: 'normal',
  fontStyle: 'normal',
};
