import {useMutation, useQueryClient} from '@tanstack/react-query';
import {useAuthStore} from '@/store/authStore';
import api, {tokenStorage} from '@/services/api';

// Query keys
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  status: () => [...authKeys.all, 'status'] as const,
  kioskLogin: () => [...authKeys.all, 'kioskLogin'] as const,
};

/**
 * Interface for kiosk login response
 */
interface KioskLoginResponse {
  data: {
    accessToken: string;
    display_name: string;
    email: string;
    id: string;
    is_chat_on: boolean;
    is_notification_on: boolean;
    name: string;
    refreshToken: string;
    status: number;
    user_type: string;
    image?: string;
  };
  message?: string;
}

/**
 * API function for kiosk login
 * @param token - QR code token
 */
const kioskLoginApi = async (token: string) => {
  const response = await api.post<KioskLoginResponse>('/user/kiosk-login', {
    token,
  });

  if (response.status >= 200 && response.status < 300) {
    return response.data;
  }

  throw new Error(response.data?.message || 'Login failed');
};

/**
 * Hook for kiosk login
 */
export const useKioskLogin = () => {
  const queryClient = useQueryClient();
  const login = useAuthStore(state => state.login);

  return useMutation({
    mutationKey: authKeys.kioskLogin(),
    mutationFn: kioskLoginApi,
    onSuccess: response => {
      const userData = response.data;

      console.log('userData', response);

      const {accessToken: newAccessToken, refreshToken: newRefreshToken} =
        response.data;

      // Save new tokens
      tokenStorage.set('accessToken', newAccessToken);
      tokenStorage.set('refreshToken', newRefreshToken);
      // Store authentication data in the auth store
      login(userData);

      // Invalidate relevant queries
      queryClient.invalidateQueries({queryKey: authKeys.status()});
      queryClient.invalidateQueries({queryKey: authKeys.user()});

      // Set user data in the query cache
      queryClient.setQueryData(authKeys.user(), userData);
    },
  });
};

/**
 * Hook for logging out
 */
export const useLogout = () => {
  const queryClient = useQueryClient();
  const logout = useAuthStore(state => state.logout);

  return useMutation({
    mutationFn: async () => {
      // This would typically call a logout API endpoint
      // For now, just return a successful response
      return {success: true};
    },
    onSuccess: () => {
      // Update auth store
      logout();

      // Invalidate all queries to ensure fresh data on re-login
      queryClient.invalidateQueries();

      // Explicitly clear specific caches
      queryClient.invalidateQueries({queryKey: authKeys.status()});
      queryClient.invalidateQueries({queryKey: authKeys.user()});

      // Clear user data from the query cache
      queryClient.setQueryData(authKeys.user(), null);

      // Reset query client to initial state
      queryClient.resetQueries();
    },
  });
};
