/**
 * @fileoverview Splash Screen component - Initial loading screen for the Kiosk application
 * @module Screens/SplashScreen
 * @category Screens
 */

import React, {useEffect} from 'react';
import {StyleSheet, Animated, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../navigation';
import images from '../config/images';
import FastImage from 'react-native-fast-image';

// Define the props type with optional onFinish
interface SplashScreenProps {
  onFinish?: () => void;
  duration?: number;
}

// Type for navigation
type SplashScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'Splash'
>;

const SplashScreen: React.FC<SplashScreenProps> = ({
  onFinish,
  duration = 2000, // Default duration of 2 seconds
}) => {
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const navigation = useNavigation<SplashScreenNavigationProp>();

  useEffect(() => {
    // Fade in animation
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.delay(duration - 1200),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // After animation completes, call the onFinish callback or navigate
      if (onFinish) {
        onFinish();
      } else {
        navigation.replace('Welcome');
      }
    });
  }, [fadeAnim, duration, onFinish, navigation]);

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.contentContainer, {opacity: fadeAnim}]}>
        <FastImage
          source={images.fullLogo}
          style={styles.logo}
          resizeMode="contain"
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  contentContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 534.031,
    height: 168.06,
  },
});

export default SplashScreen;
