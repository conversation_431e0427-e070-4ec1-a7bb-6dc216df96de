<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Kiosk</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIAppFonts</key>
	<array>
		<!-- Helvetica Neue fonts -->
		<string>HelveticaNeueLTStd-Th.otf</string>
		<string>HelveticaNeueLTStd-Lt.otf</string>
		<string>HelveticaNeueLTStd-Roman.otf</string>
		<string>HelveticaNeueLTStd-Md.otf</string>
		<string>HelveticaNeueLTStd-Bd.otf</string>
		<string>HelveticaNeueLTStd-Blk.otf</string>

		<!-- Cocogoose fonts -->
		<string>CocogooseClassic-Thin.ttf</string>
		<string>CocogooseClassic-ThinItalic.ttf</string>
		<string>CocogooseClassic-ExtraLight.ttf</string>
		<string>CocogooseClassic-ExtraLightIt.ttf</string>
		<string>CocogooseClassic-Light.ttf</string>
		<string>CocogooseClassic-LightItalic.ttf</string>
		<string>CocogooseClassic-Regular.ttf</string>
		<string>CocogooseClassic-Italic.ttf</string>
		<string>CocogooseClassic-Medium.ttf</string>
		<string>CocogooseClassic-MediumItalic.ttf</string>
		<string>CocogooseClassic-Bold.ttf</string>
		<string>CocogooseClassic-BoldItalic.ttf</string>
		<string>CocogooseClassic-ExtraBold.ttf</string>
		<string>CocogooseClassic-ExtraBoldIt.ttf</string>
		<string>CocogooseClassic-Black.ttf</string>
		<string>CocogooseClassic-BlackItalic.ttf</string>

		<!-- Icon font -->
		<string>icomoon.ttf</string>
	</array>
</dict>
</plist>
