import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import images from '@/config/images';
import {MainStackParamList} from '@/navigation';
import {useLanguageStore} from '@/store/languageStore';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';

export type workOnFinerPointsProps = {
  route: RouteProp<MainStackParamList, 'workOnFinerPoints'>;
};

const WorkOnFinerPoints = ({route}: workOnFinerPointsProps) => {
  const {from} = route.params || {};
  const {t} = useLanguageStore();
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  const handlePress = () => {
    // if (from === 'racquet') {
    navigation.navigate('ProductOptions', {
      from: from,
      item: {
        title: 'racquetDetailsExp.title',
        description: 'racquetDetailsExp.description',
        image: images.dunlopLX800,
        handleImage: images.dunlopLX800Handle,
        colors: [{value: '#0050A4', label: '#0050A4'}],
      },
    });
    // } else {
    //   navigation.navigate('RacquetDetails', {
    //     from: from,
    //     racquetData: ComfortableRacquetData,
    //   });
    // }
  };

  const handlePress2 = () => {
    // if (from === 'racquet') {
    navigation.navigate('ProductOptions', {
      from: from,
      item: {
        title: 'racquetDetailsExp.title',
        description: 'racquetDetailsExp.description',
        image: images.dunlopFx500,
        handleImage: images.dunlopCx500Handle,
        colors: [{value: '#0050A4', label: '#0050A4'}],
      },
    });
    // } else {
    //   navigation.navigate('RacquetDetails', {
    //     from: from,
    //     racquetData: PowerRacquetData,
    //   });
    // }
  };

  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        description={t('workOnFinerPoints.description')}
        btnText1={{
          text: t('workOnFinerPoints.btnText1_1'),
          text2: t('workOnFinerPoints.btnText1_2'),
        }}
        btnText2={{
          text: t('workOnFinerPoints.btnText2_1'),
          text2: '',
        }}
        onPress={() => {
          handlePress();
        }}
        onPress2={() => {
          handlePress2();
        }}
      />
    </GradientBackground>
  );
};

export default WorkOnFinerPoints;
