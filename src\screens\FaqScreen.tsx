import React, {useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager,
  Animated,
} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import GradientBackground from '../components/GradientBackground';
import KioskConfig from '../config/KioskConfig';
import Typography from '../components/Typography';
import {FONT_SIZE} from '../utils/fontSizes';
import Icon from '@components/Icon';
import {moderateScale, scale, verticalScale} from '@/utils/scale';

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQItemProps {
  item: FAQItem;
}

// Enable layout animations for Android
if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const FaqScreen = () => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredFaqs, setFilteredFaqs] = useState<FAQItem[]>([]);

  // Sample FAQ data - replace with your actual FAQs
  const faqData: FAQItem[] = [
    {
      question: "How do I purchase the racquet I demo'd?",
      answer:
        "You can order the same model using the touchscreen on the smart kiosk. Once purchased, it will be delivered to a kiosk locker in 48 hours, and you will be notified that it's ready for pick-up. If you would like more purchase options, including having your equipment delivered to your home, office, or elsewhere, use the free GoRaqt app, available for download at the App Store.",
    },
    {
      question: 'How do I apply my discount?',
      answer:
        'Enter your promo code in the indicated field on the checkout screen and it will be applied to your total.',
    },
    {
      question: 'How do I recycle old tennis balls?',
      answer:
        'Drop off your old tennis balls in the door marked "Recycle" on the lower portion of the kiosk. The earth thanks you for making the effort.',
    },
    {
      question: 'What can I have delivered to the locker?',
      answer:
        'Any gear available through the GoRaqt kiosk and the GoRaqt app can be delivered to a kiosk locker for pick-up. The app offers a much wider range of racquet sports gear to choose from.',
    },
    {
      question: 'How do I get my tennis racquet restrung?',
      answer:
        "Use the home screen of the kiosk touchscreen to order stringing. Upon check-out, you'll be assigned a locker and a code to access it. Leave your racquet in the locker. When it's ready (usually 24 to 48 hours), you'll be notified to pick it up from the same locker.",
    },
    {
      question: 'How do I get a new grip for my racquet, bat, or paddle?',
      answer:
        "Use the home screen of the kiosk touchscreen to order a new grip. Upon check-out, you'll be assigned a locker and a code to access it. Leave your racquet, bat, or paddle in the locker. When it's ready (usually 24 to 48 hours), you'll be notified to pick it up from the same locker.",
    },
  ];

  // Filter FAQs based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredFaqs(faqData);
    } else {
      const lowercaseQuery = searchQuery.toLowerCase();
      const filtered = faqData.filter(
        item =>
          item.question.toLowerCase().includes(lowercaseQuery) ||
          item.answer.toLowerCase().includes(lowercaseQuery),
      );
      setFilteredFaqs(filtered);
    }
  }, [searchQuery]);

  // Initialize with all FAQs
  useEffect(() => {
    setFilteredFaqs(faqData);
  }, []);

  // FAQItem component
  const FAQItem: React.FC<FAQItemProps> = ({item}) => {
    const [expanded, setExpanded] = useState(false);
    const [rotationValue] = useState(new Animated.Value(0));

    const toggleExpand = () => {
      // Configure animation for content expansion
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);

      // Animate the chevron rotation
      Animated.timing(rotationValue, {
        toValue: expanded ? 0 : 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      setExpanded(!expanded);
    };

    // Interpolate rotation value for chevron animation
    const rotateZ = rotationValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '180deg'],
    });

    return (
      <View style={styles.faqItem}>
        <TouchableOpacity
          style={styles.questionContainer}
          onPress={toggleExpand}
          activeOpacity={0.7}>
          <Typography
            variant="faqTitle"
            color={KioskConfig.theme.colors.white}
            style={{width: '96%'}}>
            {item.question}
          </Typography>
          <Animated.View
            style={[styles.chevronContainer, {transform: [{rotateZ}]}]}>
            <View style={styles.chevron} />
          </Animated.View>
        </TouchableOpacity>

        {expanded && (
          <TouchableOpacity activeOpacity={1} style={styles.answerContainer}>
            <Typography
              variant="faqAnswer"
              color={KioskConfig.theme.colors.white}>
              {item.answer}
            </Typography>
          </TouchableOpacity>
        )}
        <View style={styles.divider} />
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.contentContainer}>
        <Typography
          variant="faqTitle"
          color={KioskConfig.theme.colors.white}
          style={styles.title}>
          Frequently asked questions
        </Typography>
        {/* <View style={styles.searchInputContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder="Type your question"
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={KioskConfig.theme.colors.text.inputText}
          />
          <View style={styles.searchIconContainer}>
            <View style={styles.searchIcon}>
              <Icon
                name="search-1"
                size={scale(40)}
                color={KioskConfig.theme.colors.text.inputText}
              />
            </View>
          </View>
        </View> */}

        <View style={styles.faqListContainer}>
          <FlatList
            data={filteredFaqs}
            renderItem={({item}) => <FAQItem item={item} />}
            keyExtractor={(item, index) => `faq-${index}`}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    marginHorizontal: 'auto',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: scale(80),
    maxWidth: scale(1190),
  },
  title: {
    marginBottom: scale(30),
    alignSelf: 'flex-start',
  },
  searchInputContainer: {
    width: '100%',
    backgroundColor: KioskConfig.theme.colors.lightGray,
    borderRadius: scale(50),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: scale(40),
    position: 'relative',
    overflow: 'hidden',
  },
  searchInput: {
    flex: 1,
    height: verticalScale(104),
    fontSize: FONT_SIZE.heading2,
    lineHeight: verticalScale(48),
    color: KioskConfig.theme.colors.text.inputText,
    paddingLeft: scale(40),
    fontWeight: '200',
    justifyContent: 'center',
  },
  searchIconContainer: {
    width: scale(65),
    height: scale(65),
    position: 'absolute',
    right: scale(0),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: scale(40),
  },
  searchIcon: {
    height: verticalScale(50),
    width: scale(50),
    resizeMode: 'contain',
  },
  searchIconCircle: {
    width: scale(16),
    height: verticalScale(16),
    borderWidth: 2,
    borderColor: '#555',
    borderRadius: scale(10),
    position: 'absolute',
    top: scale(0),
    left: scale(0),
  },
  searchIconHandle: {
    width: scale(2),
    height: verticalScale(10),
    backgroundColor: '#555',
    position: 'absolute',
    bottom: 0,
    right: 0,
    transform: [{rotate: '-45deg'}],
  },
  faqListContainer: {
    width: '100%',
    flex: 1,
    marginTop: scale(30),
  },
  listContent: {
    paddingBottom: scale(40),
    flexGrow: 1,
  },
  faqItem: {
    borderRadius: scale(20),
    marginBottom: scale(15),
    overflow: 'hidden',
  },
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: scale(25),
  },
  questionText: {},
  chevronContainer: {
    width: scale(24),
    height: verticalScale(24),
    justifyContent: 'center',
    alignItems: 'center',
  },
  chevron: {
    width: scale(28),
    height: verticalScale(28),
    borderRightWidth: 3,
    borderBottomWidth: 3,
    borderColor: KioskConfig.theme.colors.white,
    transform: [{rotate: '45deg'}],
  },
  answerContainer: {
    padding: scale(25),
    paddingTop: scale(0),
  },
  answerText: {
    fontSize: scale(48),
    color: KioskConfig.theme.colors.white,
    lineHeight: verticalScale(50),
    fontWeight: '300',
  },
  divider: {
    height: scale(1),
    backgroundColor: KioskConfig.theme.colors.white,
    opacity: 0.2,
    marginHorizontal: scale(25),
  },
});

export default FaqScreen;
