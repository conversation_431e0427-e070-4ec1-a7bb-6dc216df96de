import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import {MainStackParamList} from '@/navigation';
import {RouteProp, useNavigation} from '@react-navigation/native';
import React from 'react';
import {StackNavigationProp} from '@react-navigation/stack';
import {useLanguageStore} from '@/store/languageStore';
export type ExperienceGuideProps = {
  route: RouteProp<MainStackParamList, 'ExperienceGuide'>;
};

const ExperienceGuide = ({route}: ExperienceGuideProps) => {
  const {from} = route.params || {};
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        onPress={() => {
          navigation.navigate('zeroTotwoYearExperience', {from: from});
        }}
        btnText1={{
          text: t('experienceGuide.btnText1'),
          text2: '',
        }}
        btnText2={{
          text: t('experienceGuide.btnText2'),
          text2: '',
        }}
        onPress2={() => {
          navigation.navigate('twoPlusMoreExperience', {from: from});
        }}
      />
    </GradientBackground>
  );
};

export default ExperienceGuide;
