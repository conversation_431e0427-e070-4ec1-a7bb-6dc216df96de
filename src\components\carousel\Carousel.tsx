import React, {useState, useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  useAnimatedReaction,
} from 'react-native-reanimated';
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import {scale} from '@/utils/scale';
import CarouselNavigation from './CarouselNavigation';

export interface CarouselItem {
  id: string | number;
  [key: string]: any;
}

interface CarouselProps {
  data: CarouselItem[];
  renderItem: (
    item: CarouselItem,
    index: number,
    animatedValue: Animated.SharedValue<number>,
  ) => React.ReactNode;
  itemWidth?: number;
  itemGap?: number;
  showNavigation?: boolean;
  onItemPress?: (item: CarouselItem, index: number) => void;
}

const Carousel: React.FC<CarouselProps> = ({
  data,
  renderItem,
  itemWidth = scale(300),
  itemGap = 20,
  showNavigation = true,
  onItemPress,
}) => {
  // Shared values for the carousel
  const translateX = useSharedValue(0);
  const startX = useSharedValue(0);
  const carouselWidth = itemWidth * data.length + itemGap * (data.length - 1);

  // State for navigation buttons
  const [isFirstItem, setIsFirstItem] = useState(true);
  const [isLastItem, setIsLastItem] = useState(false);

  // Update the first/last item state based on the current position
  const updateItemState = useCallback(
    (position: number) => {
      const isFirst = position >= 0;
      const isLast = position <= -(carouselWidth - itemWidth - itemGap);

      setIsFirstItem(isFirst);
      setIsLastItem(isLast);
    },
    [carouselWidth, itemWidth, itemGap],
  );

  // Monitor the translateX value and update the item state
  useAnimatedReaction(
    () => translateX.value,
    currentPosition => {
      runOnJS(updateItemState)(currentPosition);
    },
  );

  // Pan gesture for the carousel
  const panGesture = Gesture.Pan()
    .onBegin(() => {
      startX.value = translateX.value;
    })
    .onUpdate(e => {
      'worklet';
      // Calculate the new position with boundaries
      const newPosition = Math.max(
        Math.min(startX.value + e.translationX, 0),
        -(carouselWidth - itemWidth),
      );
      translateX.value = newPosition;
    })
    .onEnd(e => {
      'worklet';
      // Calculate the nearest item position when the gesture ends
      const velocity = e.velocityX;
      const currentPosition = translateX.value;

      // Calculate the nearest item index based on the current position
      let nearestItemIndex = Math.round(
        -currentPosition / (itemWidth + itemGap),
      );

      // Adjust for velocity to create a natural feel
      if (Math.abs(velocity) > 500) {
        nearestItemIndex =
          velocity > 0
            ? Math.max(0, nearestItemIndex - 1)
            : Math.min(data.length - 1, nearestItemIndex + 1);
      }

      // Ensure the index is within bounds
      nearestItemIndex = Math.max(
        0,
        Math.min(data.length - 1, nearestItemIndex),
      );

      // Calculate the target position
      const targetPosition = -nearestItemIndex * (itemWidth + itemGap);

      // Animate to the target position
      translateX.value = withSpring(targetPosition, {
        damping: 20,
        stiffness: 150,
        mass: 1,
      });
    });

  // Animated style for the carousel container
  const carouselStyle = useAnimatedStyle(() => {
    return {
      transform: [{translateX: translateX.value}],
    };
  });

  // Navigate to previous item
  const goToPrevious = useCallback(() => {
    if (!isFirstItem) {
      // Calculate current index based on current position
      const currentIndex = Math.round(
        -translateX.value / (itemWidth + itemGap),
      );
      // Calculate previous index, ensuring we don't go below 0
      const prevIndex = Math.max(0, currentIndex - 1);
      // Calculate the target position
      const targetPosition = -prevIndex * (itemWidth + itemGap);

      // Animate to the target position
      translateX.value = withSpring(targetPosition, {
        damping: 20,
        stiffness: 150,
        mass: 1,
      });
    }
  }, [isFirstItem, translateX, itemWidth, itemGap]);

  // Navigate to next item
  const goToNext = useCallback(() => {
    if (!isLastItem) {
      // Calculate current index based on current position
      const currentIndex = Math.round(
        -translateX.value / (itemWidth + itemGap),
      );
      // Calculate next index, ensuring we don't go beyond the array bounds
      const nextIndex = Math.min(data.length - 1, currentIndex + 1);
      // Calculate the target position
      const targetPosition = -nextIndex * (itemWidth + itemGap);

      // Animate to the target position
      translateX.value = withSpring(targetPosition, {
        damping: 20,
        stiffness: 150,
        mass: 1,
      });
    }
  }, [isLastItem, translateX, itemWidth, itemGap, data.length]);

  // Handle item press
  const handleItemPress = useCallback(
    (item: CarouselItem, index: number) => {
      if (onItemPress) {
        onItemPress(item, index);
      }
    },
    [onItemPress],
  );

  return (
    <View style={styles.container}>
      <GestureHandlerRootView style={styles.gestureRoot}>
        <GestureDetector gesture={panGesture}>
          <Animated.View style={[styles.carouselContainer, carouselStyle]}>
            {data.map((item, index) => (
              <View key={item.id || index} style={[styles.itemContainer]}>
                {renderItem(item, index, translateX)}
              </View>
            ))}
          </Animated.View>
        </GestureDetector>

        {showNavigation && (
          <CarouselNavigation
            isFirstItem={isFirstItem}
            isLastItem={isLastItem}
            onPrevious={goToPrevious}
            onNext={goToNext}
          />
        )}
      </GestureHandlerRootView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  gestureRoot: {
    flex: 1,
  },
  carouselContainer: {
    flexDirection: 'row',
    paddingHorizontal: scale(16),
  },
  itemContainer: {
    marginRight: scale(55),
  },
});

export default Carousel;
