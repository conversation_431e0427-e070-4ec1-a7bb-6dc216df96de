/* eslint-disable @typescript-eslint/no-require-imports */
import {ImageRequireSource} from 'react-native';

type Image = {
  [key: string]: ImageRequireSource;
};

const images: Image = {
  // Using optimized WebP images
  home: require('../assest/Images/optimized/Home.webp'),
  back: require('../assest/Images/optimized/Back.webp'),
  rightArrow: require('../assest/Images/optimized/right.webp'),
  leftArrow: require('../assest/Images/optimized/left.webp'),
  search: require('../assest/Images/optimized/search.webp'),
  fullLogo: require('../assest/Images/optimized/fulllogo.webp'),
  ball: require('../assest/Images/optimized/ball.webp'),
  ball2: require('../assest/Images/optimized/Dunlop-Balls-Details.webp'),
  balls: require('../assest/Images/optimized/balls.webp'),
  ballManufacturingProcess: require('../assest/Images/optimized/ballManufacturing.webp'),
  ballTechnology: require('../assest/Images/optimized/ballTechnology.webp'),
  graphicTraining: require('../assest/Images/optimized/GraphicTraining.webp'),

  // App Background
  appBackground: require('../assest/Images/optimized/LayoutBackground.webp'),
  // App Demo Images removed as requested
  // App Demo Images (Optimized WebP)
  appDemo1: require('../assest/Images/optimizedAppDemo/appDemo1.webp'),
  appDemo2: require('../assest/Images/optimizedAppDemo/appDemo2.webp'),
  appDemo3: require('../assest/Images/optimizedAppDemo/appDemo3.webp'),
  appDemo4: require('../assest/Images/optimizedAppDemo/appDemo4.webp'),
  appDemo5: require('../assest/Images/optimizedAppDemo/appDemo5.webp'),
  appDemo6: require('../assest/Images/optimizedAppDemo/appDemo6.webp'),
  appDemo7: require('../assest/Images/optimizedAppDemo/appDemo7.webp'),
  appDemo8: require('../assest/Images/optimizedAppDemo/appDemo8.webp'),
  appDemo9: require('../assest/Images/optimizedAppDemo/appDemo9.webp'),
  appDemo10: require('../assest/Images/optimizedAppDemo/appDemo10.webp'),
  appDemo11: require('../assest/Images/optimizedAppDemo/appDemo11.webp'),
  appDemo12: require('../assest/Images/optimizedAppDemo/appDemo12.webp'),
  appDemo13: require('../assest/Images/optimizedAppDemo/appDemo13.webp'),
  appDemo14: require('../assest/Images/optimizedAppDemo/appDemo14.webp'),
  appDemo15: require('../assest/Images/optimizedAppDemo/appDemo15.webp'),
  appDemo16: require('../assest/Images/optimizedAppDemo/appDemo16.webp'),
  appDemo17: require('../assest/Images/optimizedAppDemo/appDemo17.webp'),
  appDemo18: require('../assest/Images/optimizedAppDemo/appDemo18.webp'),
  appDemo19: require('../assest/Images/optimizedAppDemo/appDemo19.webp'),
  appDemo20: require('../assest/Images/optimizedAppDemo/appDemo20.webp'),
  appDemo21: require('../assest/Images/optimizedAppDemo/appDemo21.webp'),
  appDemo22: require('../assest/Images/optimizedAppDemo/appDemo22.webp'),
  appDemo23: require('../assest/Images/optimizedAppDemo/appDemo23.webp'),
  appDemo24: require('../assest/Images/optimizedAppDemo/appDemo24.webp'),
  appDemo25: require('../assest/Images/optimizedAppDemo/appDemo25.webp'),
  appDemo26: require('../assest/Images/optimizedAppDemo/appDemo26.webp'),
  appDemo27: require('../assest/Images/optimizedAppDemo/appDemo27.webp'),
  appDemo28: require('../assest/Images/optimizedAppDemo/appDemo28.webp'),
  appDemo29: require('../assest/Images/optimizedAppDemo/appDemo29.webp'),
  appDemo30: require('../assest/Images/optimizedAppDemo/appDemo30.webp'),
  appDemo31: require('../assest/Images/optimizedAppDemo/appDemo31.webp'),
  appDemo32: require('../assest/Images/optimizedAppDemo/appDemo32.webp'),
  appDemo33: require('../assest/Images/optimizedAppDemo/appDemo33.webp'),
  appDemo34: require('../assest/Images/optimizedAppDemo/appDemo34.webp'),
  appDemo35: require('../assest/Images/optimizedAppDemo/appDemo35.webp'),
  appDemo36: require('../assest/Images/optimizedAppDemo/appDemo36.webp'),
  appDemo37: require('../assest/Images/optimizedAppDemo/appDemo37.webp'),
  appDemo38: require('../assest/Images/optimizedAppDemo/appDemo38.webp'),
  // Video
  racquet: require('../assest/Images/optimized/Racquet.webp'),
  padel: require('../assest/Images/optimized/padel.webp'),
  userProfile: require('../assest/Images/optimized/user_profile.webp'),

  hello: require('../assest/Images/optimized/hello.webp'),
  qrCode: require('../assest/Images/optimized/QRCode.webp'),

  // Branded Balls
  dunlopBalls_ATPTour: require('../assest/Images/optimized/Dunlop-Balls-ATPTour.webp'),

  dunlop: require('../assest/Images/optimized/dunlop.webp'),
  yonex: require('../assest/Images/optimized/yonex.webp'),
  wilson: require('../assest/Images/optimized/wilson.webp'),
  head: require('../assest/Images/optimized/head.webp'),
  penn: require('../assest/Images/optimized/penn.webp'),

  //ball list
  // atp: require('../assest/Images/ao.png'),
  // atpBlack: require('../assest/Images/atpblack.png'),
  atpBlue: require('../assest/Images/optimized/atpblue.webp'),
  grand: require('../assest/Images/optimized/grand.webp'),
  // aoBall: require('../assest/Images/ao.png'),
  atpBall: require('../assest/Images/optimized/atp.webp'),
  // atpBlackBall: require('../assest/Images/atpblack.png'),
  grandBlackBall: require('../assest/Images/optimized/grandBlack.webp'),

  //racquet list
  dunlopCx200: require('../assest/Images/optimized/Dunlop-CX200.webp'),
  dunlopFx500: require('../assest/Images/optimized/Dunlop-FX500.webp'),
  dunlopSx300: require('../assest/Images/optimized/Dunlop-SX300.webp'),
  dunlopLX800: require('../assest/Images/optimized/Dunlop-LX800.webp'),

  // racquets
  dunlopCx200Handle: require('../assest/Images/optimized/Dunlop-CX200-Handle.webp'),
  dunlopCx500Handle: require('../assest/Images/optimized/Dunlop-FX500-Handle.webp'),
  dunlopLX800Handle: require('../assest/Images/optimized/Dunlop-LX800-handle.webp'),
  dunlopSx300Handle: require('../assest/Images/optimized/Dunlop-SX300-Handle.webp'),
  RotateRacquet: require('../assest/Images/optimized/rotateRacquet.webp'),

  hand1: require('../assest/Images/optimized/hand1.webp'),
  hand2: require('../assest/Images/optimized/hand2.webp'),
  hand3: require('../assest/Images/optimized/hand3.webp'),

  // padelBrandList
  adidas: require('../assest/Images/optimized/adidas.webp'),
  babolat: require('../assest/Images/optimized/babolat.webp'),
  nox: require('../assest/Images/optimized/nox.webp'),
  bullpadel: require('../assest/Images/optimized/bullpadel.webp'),

  //padel list
  aeroStarPro: require('../assest/Images/optimized/aero-star-pro.webp'),
  galacticaPro: require('../assest/Images/optimized/galactica-pro.webp'),
  galacticaProLs: require('../assest/Images/optimized/galactica-pro-ls.webp'),

  technifibre: require('../assest/Images/optimized/Technifibre.webp'),

  //Tennis ball list
  // AoBall: require('../assest/Images/Ao1.png'),
  AtpChamp: require('../assest/Images/optimized/ATPChamp.webp'),
  AtpTour: require('../assest/Images/optimized/ATPTour.webp'),
  GrandPrix: require('../assest/Images/optimized/GrandPrix.webp'),
  Stage1: require('../assest/Images/optimized/Stage1Box.webp'),
  Stage2: require('../assest/Images/optimized/Stage2Box.webp'),
  NewAo: require('../assest/Images/optimized/NewAo.webp'),

  atp1: require('../assest/Images/optimized/atp1.webp'),
  grandPrix1: require('../assest/Images/optimized/grandPrix1.webp'),
  dunlop1: require('../assest/Images/optimized/dunlop1.webp'),
  aoBox: require('../assest/Images/optimized/AoBox.webp'),
  stage1Ball: require('../assest/Images/optimized/stageBall1.webp'),
  stage2: require('../assest/Images/optimized/stageBall2.webp'),

  // Ball Images
  AOBall: require('../assest/Images/optimized/AO-Ball.webp'),
  ATPBall: require('../assest/Images/optimized/ATPBall.webp'),
  GrandPrixBall: require('../assest/Images/optimized/GrandPrixBall.webp'),
  Stage2Ball: require('../assest/Images/optimized/Stage2Ball.webp'),
};

export default images;
