/**
 * Common dimensions and spacing values for the app
 *
 * This file provides standardized spacing and typography values
 * that can be used throughout the app for consistent UI.
 */
import {scale, verticalScale, moderateScale} from './scale';

/**
 * Standard spacing values for margins, paddings, etc.
 */
export const spacing = {
  xs: scale(4),
  s: scale(8),
  m: scale(16),
  l: scale(24),
  xl: scale(32),
  xxl: scale(48),
};

/**
 * Typography sizes that complement the existing FONT_SIZE
 * These are scaled versions that adapt to different screen sizes
 */
export const typography = {
  xs: scale(10),
  sm: scale(12),
  md: scale(14),
  lg: scale(16),
  xl: scale(18),
  xxl: scale(20),
  title: scale(24),
  heading: scale(32),
  display: scale(48),
};

/**
 * Common border radius values
 */
export const borderRadius = {
  xs: scale(2),
  s: scale(4),
  m: scale(8),
  l: scale(16),
  xl: scale(24),
  round: scale(999), // For circular elements
};

/**
 * Common icon sizes
 */
export const iconSize = {
  xs: scale(16),
  s: scale(24),
  m: scale(32),
  l: scale(48),
  xl: scale(64),
};

/**
 * Helper function to create responsive styles
 * @param styles Object containing style properties with pixel values
 * @returns Object with scaled style properties
 */
export const createResponsiveStyles = (styles: Record<string, any>) => {
  const scaledStyles: Record<string, any> = {};

  for (const key in styles) {
    const value = styles[key];

    // Scale width, height, margin, padding properties
    if (
      [
        'width',
        'height',
        'maxWidth',
        'maxHeight',
        'minWidth',
        'minHeight',
      ].includes(key) ||
      key.includes('margin') ||
      key.includes('padding')
    ) {
      if (typeof value === 'number') {
        scaledStyles[key] = scale(value);
      } else {
        scaledStyles[key] = value; // Keep percentage or other values as is
      }
    }
    // Scale font size
    else if (key === 'fontSize') {
      scaledStyles[key] = scale(value);
    }
    // Keep other properties as is
    else {
      scaledStyles[key] = value;
    }
  }

  return scaledStyles;
};
