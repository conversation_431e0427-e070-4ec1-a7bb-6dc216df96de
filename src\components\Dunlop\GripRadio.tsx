import {FONTS} from '@/utils/fonts';
import {moderateScale, scale} from '@/utils/scale';
import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Svg, {Polygon, Text} from 'react-native-svg';

interface RadioSelectProps {
  label: string;
  selected: boolean;
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

const GripRadio: React.FC<RadioSelectProps> = ({
  label,
  selected,
  onPress,
  containerStyle,
  textStyle,
}) => {
  // Define the octagon properties
  const size = scale(54); // Slightly larger size
  const cornerSize = size * 0.3;
  const center = size / 2;

  const points = [
    cornerSize,
    0,
    size - cornerSize,
    0,
    size,
    cornerSize,
    size,
    size - cornerSize,
    size - cornerSize,
    size,
    cornerSize,
    size,
    0,
    size - cornerSize,
    0,
    cornerSize,
  ].join(' ');

  return (
    <View style={[styles.container, containerStyle]}>
      <TouchableOpacity activeOpacity={0.7} onPress={onPress}>
        <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
          <Polygon
            points={points}
            fill={selected ? '#DFFC4F' : 'transparent'}
            stroke={selected ? 'transparent' : '#ffffff'}
            strokeWidth={selected ? 0 : 1.3}
          />
          <Text
            x={center}
            y={center + 7} // Adjusted for vertical centering
            fontSize={scale(19)}
            fontFamily={FONTS.cocogoose}
            fill={selected ? '#000000' : '#ffffff'} // Black text on yellow background, white on transparent
            textAnchor="middle">
            {label}
          </Text>
        </Svg>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    marginHorizontal: scale(5),
  },
});

export default GripRadio;
