import React from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '@/navigation';
import ProductImage from '@/components/ProductImage';
import {StackNavigationProp} from '@react-navigation/stack';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';

interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

const CustomisePurchaseDetails = ({
  route,
}: {
  route: RouteProp<MainStackParamList, 'CustomisePurchaseDetails'>;
}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  const {padelData} = route.params || {};

  const {t} = useLanguageStore();

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: 'Grip',
      dropdown: true,
      items: [
        {label: 'Grip', value: 'grip'},
        {label: 'Grip', value: 'grip'},
        {label: 'Grip', value: 'grip'},
      ],
      onSelectItem: () => {
        navigation.navigate('PadelCheckOutScreen', {
          padelData: padelData,
        });
      },
    },
  ];

  const renderRacquetDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            title={t(padelData?.title)}
            description={t(padelData?.description)}
            actionButtons={actionButtons}
            colors={padelData?.colors}
          />
        </View>
        <View style={styles.imageContainer}>
          <ProductImage image={padelData?.image} imageStyle={styles.image} />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderRacquetDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    margin: 'auto',
    flexDirection: 'row',
    position: 'relative',
    justifyContent: 'space-between',
    flex: 1,
  },
  contentContainer: {
    width: '35%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    height: '100%',
  },
  imageContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    flex: 1,
  },
  image: {
    width: scale(577),
    height: verticalScale(961),
  },
});
export default CustomisePurchaseDetails;
