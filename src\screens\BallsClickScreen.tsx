import React, {useState} from 'react';
import GradientBackground from '../components/GradientBackground';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Typography from '@/components/Typography';
import KioskConfig from '@/config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import WelcomeToGoRactModal from '@/components/WelcomeToGoRactModal';

const BallsClickScreen = ({route}: {route: any}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  //   const {from} = route.params;

  const [visible, setVisible] = useState<boolean>(true);

  return (
    <GradientBackground>
      <View style={styles.container}>
        <TouchableOpacity activeOpacity={0.7} onPress={() => setVisible(true)}>
          <Typography
            variant="timerText"
            color={KioskConfig.theme.colors.white}
            align="center">
            {`Tap all the \n Dunlop AO balls`}
          </Typography>
        </TouchableOpacity>
      </View>
      <WelcomeToGoRactModal
        visible={visible}
        onClose={() => {
          setVisible(false);
          navigation.navigate('Congratulations');
        }}
      />
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default BallsClickScreen;
