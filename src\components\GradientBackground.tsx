import React from 'react';
import {StyleSheet, ViewStyle, View, ImageBackground} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Header from './Header';
import Footer from './Footer';
// import LottieView from 'lottie-react-native';
// import animation from '../config/animation';
import {scale} from '@/utils/scale';
import images from '@/config/images';
import {useCart} from '@/context/CartContext';

interface GradientBackgroundProps {
  children: React.ReactNode;
  style?: ViewStyle;
  backButton?: boolean;
  homeButton?: boolean;
  headerOnly?: boolean;
  useGradient?: boolean;
  useImageBackground?: boolean;
  backgroundColor?: string;
  cart?: boolean;
  onCartPress?: () => void;
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({
  children,
  style,
  backButton = true,
  homeButton = true,
  headerOnly = false,
  useGradient = true,
  useImageBackground = true,
  backgroundColor = '#000000',
  cart = false,
  onCartPress = () => {},
}) => {
  const {items} = useCart();
  const content = (
    <>
      <Header
        showBackButton={backButton}
        showHomeButton={homeButton}
        showCart={cart}
        cartPress={onCartPress}
        cartItemCount={items?.length}
      />
      {/* <LottieView
        source={animation.background}
        autoPlay
        loop
        style={styles.animation}
        speed={0.5}
      /> */}
      <View style={{flex: 1, position: 'relative', zIndex: 2}}>{children}</View>
      {!headerOnly && <Footer />}
    </>
  );

  if (!useGradient) {
    return (
      <View style={[styles.container, {backgroundColor}, style]}>
        {content}
      </View>
    );
  }

  if (useImageBackground) {
    return (
      <ImageBackground
        source={images.appBackground} // Place your image here
        style={[styles.container, style]}
        resizeMode="cover">
        {content}
      </ImageBackground>
    );
  }

  return (
    <LinearGradient
      colors={['#3B3F48', '#9299A3', '#E4E5E9']}
      start={{x: 0.5, y: 0}}
      end={{x: 0.5, y: 1}}
      style={[styles.container, style]}>
      {content}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: scale(20),
  },
  animation: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{translateX: '-50%'}, {translateY: '-50%'}],
    width: '100%',
    height: '100%',
  },
});

export default GradientBackground;
