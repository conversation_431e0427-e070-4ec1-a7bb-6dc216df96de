import React, {useEffect, useState} from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '@/navigation';
import {gripSizesData} from '@/config/staticData';
import ProductImage from '@/components/ProductImage';
import {StackNavigationProp} from '@react-navigation/stack';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';

interface ActionButton {
  id: number;
  title: string;
  dropdown?: boolean;
  items?: Array<{label: string; value: string}>;
  onSelectItem?: (item: {label: string; value: string}) => void;
  highlighted?: boolean;
}

const CustomiseRacquetDetails = ({
  route,
}: {
  route: RouteProp<MainStackParamList, 'CustomiseRacquetDetails'>;
}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const [selectedString, setSelectedString] = useState<{
    first: string;
    second: string;
    third: string;
  }>({
    first: 'Strings',
    second: 'Tension',
    third: 'Grips',
  });

  const {racquetData} = route.params || {};

  const {t} = useLanguageStore();

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: selectedString.first,
      dropdown: true,
      items: [
        {label: 'Unstrung - Do not string', value: 'unstrung'},
        {label: 'String Specials', value: 'string_specials'},
        {label: 'Favorite Value Strings', value: 'favorite_value_strings'},
        {label: 'Favorite Comfort Strings', value: 'favorite_comfort_strings'},
        {label: 'Favorite Spin Strings', value: 'favorite_spin_strings'},
        {
          label: 'Favorite Durability Strings',
          value: 'favorite_durability_strings',
        },
        {label: 'Favorite Natural Guts', value: 'favorite_natural_guts'},
        {label: 'Favorite Multifilaments', value: 'favorite_multifilaments'},
        {label: 'Favorite Synthetic Guts', value: 'favorite_synthetic_guts'},
        {
          label: 'Favorite Value Polyesters',
          value: 'favorite_value_polyesters',
        },
        {label: 'Favorite Soft Polyesters', value: 'favorite_soft_polyesters'},
        {label: 'Best Of The Rest!', value: 'best_of_the_rest'},
        {label: 'Build a Custom Hybrid', value: 'build_custom_hybrid'},
        {label: 'String Not In This Menu', value: 'string_not_in_menu'},
      ],
      onSelectItem: (item: {label: string; value: string}) => {
        setSelectedString(prev => ({
          ...prev,
          first: item.label,
        }));
      },
      highlighted: selectedString.first !== 'Strings',
    },
    {
      id: 2,
      title: selectedString.second,
      dropdown: true,
      items: [
        {label: '50 lbs', value: '50'},
        {label: '51 lbs', value: '51'},
        {label: '52 lbs', value: '52'},
        {label: '53 lbs', value: '53'},
        {label: '54 lbs', value: '54'},
        {label: '55 lbs', value: '55'},
        {label: '56 lbs', value: '56'},
        {label: '57 lbs', value: '57'},
        {label: '58 lbs', value: '58'},
        {label: '59 lbs', value: '59'},
        {label: '60 lbs', value: '60'},
        {label: '61 lbs', value: '61'},
        {label: '62 lbs', value: '62'},
        {label: '63 lbs', value: '63'},
        {label: '64 lbs', value: '64'},
        {label: '65 lbs', value: '65'},
      ],
      onSelectItem: (item: {label: string; value: string}) => {
        setSelectedString(prev => ({
          ...prev,
          second: item.label,
        }));
      },
      highlighted: selectedString.second !== 'Tension',
    },
    {
      id: 3,
      title: selectedString.third,
      dropdown: true,
      items: [
        {
          value: '4 1/8',
          label: '4 1/8',
        },
        {
          value: '4 1/4',
          label: '4 1/4',
        },
        {
          value: '4 3/8',
          label: '4 3/8',
        },
      ],
      onSelectItem: (item: {label: string; value: string}) => {
        setSelectedString(prev => ({
          ...prev,
          third: item.label,
        }));
      },
      highlighted: selectedString.third !== 'Grips',
    },
  ];

  useEffect(() => {
    // Store previous selection state to detect actual changes
    const isFirstSelection = selectedString.first !== 'Strings';
    const isSecondSelection = selectedString.second !== 'Tension';
    const isThirdSelection = selectedString.third !== 'Grips';
    // We only navigate when both selections are made
    if (isFirstSelection && isSecondSelection && isThirdSelection) {
      // Add a small delay to allow UI to update and give user visual feedback
      const timer = setTimeout(() => {
        navigation.navigate('CheckOutScreen', {
          racquetData: racquetData,
        });
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [selectedString, navigation, racquetData]);

  const renderRacquetDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            title={t(racquetData?.title || '')}
            description={t(racquetData?.description || '')}
            actionButtons={actionButtons}
            gripSizes={gripSizesData}
            colors={racquetData?.colors}
            helpMeChoosePress={() => navigation.navigate('HelpMeChooseRaquet')}
          />
        </View>
        <View style={styles.imageContainer}>
          <ProductImage image={racquetData?.image} imageStyle={styles.image} />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderRacquetDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    marginHorizontal: 'auto',
    flexDirection: 'row',
    position: 'relative',
    flex: 1,
  },
  contentContainer: {
    width: scale(489),
    height: verticalScale(1050),
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: scale(100),
    top: verticalScale(100),
  },
  image: {
    width: scale(353),
    height: verticalScale(961),
  },
  ballImage: {
    width: 283,
    height: 280,
  },
  ballImageContainer: {
    position: 'absolute',
    right: '-40%',
    bottom: '-7%',
  },
});
export default CustomiseRacquetDetails;
