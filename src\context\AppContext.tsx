/**
 * @fileoverview App Context - Main application context provider with global state management
 * @module Context/AppContext
 * @category Context
 */

import React, {createContext, ReactNode} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {CartProvider} from './CartContext';
import {ReservationProvider} from './ReservationContext';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {StatusBar} from 'react-native';

// Create a singleton QueryClient instance
const queryClient = new QueryClient();

// Context for global app state (can be expanded later)
interface AppContextProps {
  dummy?: boolean; // Placeholder property to avoid empty interface
}

export const AppContext = createContext<AppContextProps>({dummy: false});

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({children}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <ReservationProvider>
        <CartProvider>
          <SafeAreaProvider>
            <StatusBar hidden={true} />
            <AppContext.Provider value={{dummy: false}}>
              {children}
            </AppContext.Provider>
          </SafeAreaProvider>
        </CartProvider>
      </ReservationProvider>
    </QueryClientProvider>
  );
};

// Custom hook to use the AppContext
export const useAppContext = () => {
  const context = React.useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
