import CustomButton from '@/components/CustomButton';
import GradientBackground from '@/components/GradientBackground';
import Typography from '@/components/Typography';
import images from '@/config/images';
import KioskConfig from '@/config/KioskConfig';
import {BallsData, ballTypeButtonConfigs} from '@/utils/staticData';
import {MainStackParamList} from '@/navigation';
import {scale, verticalScale} from '@/utils/scale';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';
import {StyleSheet, View, ScrollView} from 'react-native';
import FastImage from 'react-native-fast-image';
import {useLanguageStore} from '@/store/languageStore';

const SelectBallByType = () => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const handleNavigate = (config: any) => {
    navigation.navigate(config.screenName, {
      ballData: BallsData[0],
      ...config.params,
    });
  };

  return (
    <GradientBackground>
      <View style={[styles.container]}>
        <View style={[styles.contentContainer]}>
          <Typography
            variant="faqAnswer"
            color={KioskConfig.theme.colors.white}>
            {t('ballByType.title')}
          </Typography>
          <ScrollView
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              gap: scale(20),
            }}>
            <View style={[styles.buttonContainer]}>
              {ballTypeButtonConfigs.buttonsConfigs.map(data => (
                <CustomButton
                  text={t(data.title)}
                  onPress={() => handleNavigate(data)}
                  key={data.id}
                  style={styles.btnStyle}
                  buttonContentContainerStyle={styles.buttonContentContainer}
                />
              ))}
            </View>
          </ScrollView>
        </View>
        <View style={styles.imageContainer}>
          <FastImage
            source={images.balls}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1440),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    margin: 'auto',
  },
  btnStyle: {
    width: scale(594),
    justifyContent: 'center',
    alignItems: 'flex-start',
    height: verticalScale(104),
    // paddingHorizontal: scale(40),
  },

  contentContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },

  searchTitle: {
    marginBottom: 0,
  },
  buttonContainer: {
    gap: scale(20),
    marginTop: scale(24),
  },
  image: {
    width: scale(712.75),
    height: verticalScale(746.17),
  },
  buttonContentContainer: {
    gap: scale(25),
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: 0,
  },
});

export default SelectBallByType;
