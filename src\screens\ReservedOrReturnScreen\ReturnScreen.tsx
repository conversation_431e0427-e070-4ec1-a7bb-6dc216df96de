import React from 'react';
import {View, StyleSheet} from 'react-native';
import GradientBackground from '../../components/GradientBackground';
import KioskConfig from '../../config/KioskConfig';
import Typography from '../../components/Typography';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../../navigation';
import CustomButton from '@/components/CustomButton';
import images from '../../config/images';
import {moderateScale, scale, verticalScale} from '@/utils/scale';
import FastImage from 'react-native-fast-image';
const ReturnScreen = () => {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  return (
    <GradientBackground>
      <View style={styles.container}>
        <View style={styles.leftSection}>
          <Typography variant="heading2" color={KioskConfig.theme.colors.white}>
            Select equipment and return
          </Typography>

          <CustomButton
            text="Return"
            style={styles.actionButton}
            onPress={() => navigation.navigate('SomethingElse')}
          />
        </View>

        <View style={styles.rightSection}>
          <FastImage
            source={images.dunlopCx200}
            style={styles.rac1}
            resizeMode="contain"
          />
          <FastImage
            source={images.dunlopSx300}
            style={styles.rac2}
            resizeMode="contain"
          />
        </View>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1440),
    margin: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: scale(150),
  },
  leftSection: {
    flex: 1,
    alignItems: 'flex-start',
    gap: scale(20),
  },
  rightSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    position: 'absolute',
    right: 0,
    top: 0,
    transform: [{translateY: '-20%'}],
  },

  timeSelectors: {
    gap: scale(25),
    marginVertical: scale(40),
  },

  timeLabel: {
    fontSize: scale(48),
    marginLeft: scale(20),
  },
  buttonContainer: {
    gap: scale(20),
  },
  actionButton: {
    alignItems: 'flex-start',
    width: scale(594),
    height: verticalScale(104),
    // paddingLeft: scale(40),
  },
  rac1: {
    width: scale(392),
    height: verticalScale(1122),
  },
  rac2: {
    width: scale(404),
    height: verticalScale(1130),
    marginLeft: scale(-170),
  },
  leftButton: {
    width: scale(60),
  },
});

export default ReturnScreen;
