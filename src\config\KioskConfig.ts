/**
 * @fileoverview Kiosk Configuration - Central configuration file for the kiosk application
 * @module Config/KioskConfig
 * @category Configuration
 */

/**
 * Main configuration object for the kiosk application.
 * Contains all settings for display, navigation, timeouts, and other kiosk-specific features.
 *
 * @constant {Object} KioskConfig
 * @example
 * import { KioskConfig } from '@/config/KioskConfig';
 *
 * // Access display settings
 * const screenWidth = KioskConfig.display.width;
 * const screenHeight = KioskConfig.display.height;
 *
 * // Check if status bar should be shown
 * if (KioskConfig.display.showStatusBar) {
 *   // Show status bar
 * }
 */
export const KioskConfig = {
  // Display settings
  display: {
    // Target resolution for 32-inch HD display (1920x1080)
    width: 1920,
    height: 1080,

    // Whether to show the status bar
    showStatusBar: false,

    // Screen timeout in milliseconds (0 = never timeout)
    // For kiosk mode, typically set to 0 to prevent screen from going to sleep
    timeout: 0,
  },

  // Application settings
  app: {
    // App name displayed in the header
    name: 'Kiosk Application',

    // Default page to navigate to after welcome screen
    defaultPage: 'Welcome',

    // Inactivity timeout in milliseconds (0 = never timeout)
    // After this period of inactivity, the app will return to the welcome screen
    inactivityTimeout: 120000, // 2 minutes
  },

  // Theme settings
  theme: {
    // Color scheme
    colors: {
      primary: '#5E81AC',
      secondary: '#81A1C1',
      background: '#1E1E2E',
      surface: '#2E2E3E',
      white: '#FFFFFF',
      lightGray: '#D9D9D9',
      black: '#000000',
      Mindaro: '#DFFF4F',
      blue: '#007AFF',
      grey: '#DDDDDD',
      activeTabBg: '#FF5A00',
      greyLight: '#3C3C43',
      text: {
        primary: '#262626',
        secondary: '#FFFFFF',
        tertiary: '#233853',
        inputText: '#383838',
        gold: '#FFD700',
        highlight: '#DFFC4F',
      },
      button: {
        background: '#5E81AC',
        text: '#FFFFFF',
      },
      border: {
        primary: '#ffffff',
        secondary: '#DFFC4F',
      },
    },

    // Font sizes
    fontSize: {
      small: 18,
      medium: 24,
      large: 36,
      xlarge: 48,
      xlarge2: 68.579,
      xxlarge: 72,
    },
  },
};

export default KioskConfig;
