# Scaling Utilities for GoRaqt Kiosk App

This document explains how to use the scaling utilities to create responsive UI elements that adapt to different screen sizes while maintaining the design proportions.

## Overview

The scaling utilities are designed for a target resolution of 1920x1080 (as defined in KioskConfig). They provide functions to scale dimensions proportionally based on the device's screen size.

## Available Functions

### Basic Scaling Functions

```typescript
import {scale, verticalScale, moderateScale} from './utils/scale';

// Horizontal scaling (width-based)
const width = scale(200); // Scales 200px horizontally

// Vertical scaling (height-based)
const height = verticalScale(60); // Scales 60px vertically

// Moderate scaling (with a factor to control the scaling intensity)
const fontSize = scale(18); // Scales with a moderate factor (default 0.5)
const largerFont = scale(18, 0.8); // Scales with a larger factor
```

### Predefined Constants

```typescript
import {spacing, typography, borderRadius, iconSize} from './utils/dimensions';

// Using spacing constants
const styles = StyleSheet.create({
  container: {
    padding: spacing.m, // Medium spacing (16px scaled)
    marginBottom: spacing.l, // Large spacing (24px scaled)
  },
});

// Using typography constants
const textStyles = StyleSheet.create({
  title: {
    fontSize: typography.title, // Title size (24px scaled)
  },
  body: {
    fontSize: typography.md, // Medium text (14px scaled)
  },
});
```

### Helper Function

```typescript
import {createResponsiveStyles} from './utils/dimensions';

// Create responsive styles with automatic scaling
const styles = StyleSheet.create({
  box: {
    ...createResponsiveStyles({
      width: 300,
      height: 150,
      padding: 20,
      borderRadius: 12,
      backgroundColor: 'lightblue',
    }),
  },
});
```

## Best Practices

1. **Use for Layout Dimensions**: Always use scaling functions for widths, heights, margins, and paddings.

2. **Use for Font Sizes**: Use `moderateScale` for font sizes to ensure readability across different screen sizes.

3. **Use Predefined Constants**: Prefer using the predefined spacing and typography constants for consistency.

4. **Percentage for Flexible Layouts**: For truly flexible layouts, combine scaling with percentage values.

5. **Test on Different Devices**: Always test your UI on different screen sizes to ensure proper scaling.

## Example Usage

See `ScalingExample.tsx` for a complete example of how to use these utilities in a component.

```typescript
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { scale, verticalScale } from './utils/scale';
import { spacing, typography } from './utils/dimensions';

const MyComponent = () => (
  <View style={styles.container}>
    <View style={styles.box} />
  </View>
);

const styles = StyleSheet.create({
  container: {
    padding: spacing.m,
  },
  box: {
    width: scale(200),
    height: verticalScale(100),
    borderRadius: scale(8),
  },
});
```
