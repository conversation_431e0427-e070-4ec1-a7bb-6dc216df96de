import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import {MainStackParamList} from '@/navigation';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';
import images from '@/config/images';
import {useLanguageStore} from '@/store/languageStore';

export type playCompititilyProps = {
  route: RouteProp<MainStackParamList, 'playCompititily'>;
};

const PlayCompititily = ({route}: playCompititilyProps) => {
  const {from} = route.params || {};
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const handlePress = () => {
    // if (from === 'racquet') {
    navigation.navigate('ProductOptions', {
      from: from,
      item: {
        title: 'racquetDetailsExp.title',
        description: 'racquetDetailsExp.description',
        image: images.dunlopCx200,
        handleImage: images.dunlopCx200Handle,
        colors: [{value: '#0050A4', label: '#0050A4'}],
      },
    });
    // } else {
    //   navigation.navigate('RacquetDetails', {
    //     from: from,
    //     racquetData: ComfortableRacquetData,
    //   });
    // }
  };

  const handlePress2 = () => {
    // if (from === 'racquet') {
    navigation.navigate('ProductOptions', {
      from: from,
      item: {
        title: 'racquetDetailsExp.title',
        description: 'racquetDetailsExp.description',
        image: images.dunlopFx500,
        handleImage: images.dunlopCx500Handle,
        colors: [{value: '#0050A4', label: '#0050A4'}],
      },
    });
    // } else {
    //   navigation.navigate('RacquetDetails', {
    //     from: from,
    //     racquetData: PowerRacquetData,
    //   });
    // }
  };

  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        description={t('playCompititily.description')}
        btnText1={{
          text: t('playCompititily.btnText1'),
          text2: '',
        }}
        btnText2={{
          text: t('playCompititily.btnText2'),
          text2: '',
        }}
        onPress={() => {
          handlePress();
        }}
        onPress2={() => {
          handlePress2();
        }}
      />
    </GradientBackground>
  );
};

export default PlayCompititily;
