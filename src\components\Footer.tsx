/**
 * @fileoverview Footer component for the Kiosk application
 * @module Components/Footer
 * @category Components
 */

import React from 'react';
import {View, Text, StyleSheet, TouchableHighlight} from 'react-native';
import KioskConfig from '../config/KioskConfig';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../navigation';
import {scale, verticalScale, moderateScale} from '@/utils/scale';
import {borderRadius} from '@/utils/dimensions';
import images from '@/config/images';
import Typography from './Typography';
import FastImage from 'react-native-fast-image';
import footerButtons, {FooterButtonConfig} from '@/config/footerConfig';
import {useLanguageStore} from '@/store/languageStore';

/**
 * Footer component for the Kiosk application
 *
 * Provides navigation buttons and help functionality at the bottom of screens.
 * Supports configurable buttons through footerConfig and internationalization.
 *
 * @component
 * @param {Object} props - The props for the Footer component
 * @param {boolean} [props.needHelp=false] - Whether to show the help button
 * @returns {JSX.Element} The rendered Footer component
 *
 * @example
 * // Basic footer without help button
 * <Footer />
 *
 * @example
 * // Footer with help button
 * <Footer needHelp={true} />
 */
const Footer: React.FC<{needHelp?: boolean}> = ({needHelp = false}) => {
  const {colors} = KioskConfig.theme;
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const {t, currentLanguage} = useLanguageStore();

  const onHelpPress = () => {
    console.log('Help pressed');
  };

  // Handle button press based on button id
  const handleButtonPress = (button: FooterButtonConfig) => {
    switch (button.id) {
      case 'accessibility':
        // Custom action for accessibility button
        console.log('Accessibility button pressed');
        // For now, navigate to Language screen as a placeholder
        navigation.navigate('Language');
        break;
      case 'faqs':
        navigation.navigate('Faq');
        break;
      case 'languages':
        navigation.navigate('Language');
        break;
      default:
        console.log(`No action defined for button: ${button.id}`);
    }
  };

  // Get background color based on button id and current screen
  const getButtonBackgroundColor = (button: FooterButtonConfig) => {
    switch (button.id) {
      case 'accessibility':
        return KioskConfig.theme.colors.Mindaro;
      default:
        return KioskConfig.theme.colors.white;
    }
  };

  return (
    <View style={[styles.container]}>
      <View style={styles.leftContent}>
        {footerButtons.map(button => (
          <TouchableHighlight
            key={button.id}
            style={{
              ...styles.languageButton,
              backgroundColor: getButtonBackgroundColor(button),
              width: currentLanguage === 'es' ? scale(155) : scale(100),
            }}
            underlayColor="#DFFC4F"
            onPress={() => handleButtonPress(button)}>
            <Text
              style={[
                styles.buttonText,
                {
                  color: colors.text.tertiary,
                },
              ]}>
              {t(button.labelKey)}
            </Text>
          </TouchableHighlight>
        ))}
      </View>

      {needHelp && (
        <View style={styles.centerContent}>
          <TouchableHighlight underlayColor="#DFFC4F" onPress={onHelpPress}>
            <Text style={[styles.helpText, {color: colors.text.tertiary}]}>
              {t('common.needHelp')}
            </Text>
          </TouchableHighlight>
        </View>
      )}

      <View style={styles.rightContent}>
        <View>
          <TouchableHighlight
            style={{
              ...styles.languageButton,
              width: scale(100),
            }}
            underlayColor="#DFFC4F"
            activeOpacity={0.7}
            onPress={() => {
              navigation.navigate('TestSDK');
            }}>
            <Text style={styles.buttonText}>Test SDK</Text>
          </TouchableHighlight>
        </View>
        <Typography
          variant="cocogooseRegular"
          style={[styles.discountText, {color: colors.black}]}>
          {t('common.downloadApp')}
        </Typography>
        <View style={styles.qrCodeContainer}>
          {/* In a real app, replace this with an actual QR code image */}
          <View style={styles.qrCode}>
            <FastImage
              source={images.qrCode}
              style={styles.qrCodeImage}
              resizeMode="contain"
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    position: 'relative',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: scale(10),
    paddingHorizontal: scale(10),
    zIndex: 3,
  },
  leftContent: {
    flex: 1,
    gap: scale(15),
    position: 'absolute',
    left: 0,
    zIndex: 2,
  },
  languageButton: {
    borderRadius: borderRadius.xl,
    alignSelf: 'flex-start',
    height: verticalScale(40),
    width: scale(100),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: KioskConfig.theme.colors.white,
  },
  buttonText: {
    fontSize: scale(14.41),
    fontWeight: 700,
    borderRadius: borderRadius.l,
  },
  centerContent: {
    flex: 1,
    alignItems: 'center',
  },
  helpText: {
    fontSize: scale(32),
    fontWeight: 700,
  },
  rightContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: scale(12.75),
    position: 'absolute',
    right: 0,
    zIndex: 2,
  },
  discountText: {
    fontSize: scale(27.35),
    lineHeight: scale(25.5),
    maxWidth: scale(170.375),
    textAlign: 'right',
    alignSelf: 'flex-end',
  },
  qrCodeContainer: {
    // width: scale(75),
    // height: scale(75),
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  qrCode: {
    width: scale(75),
    height: scale(75),
    backgroundColor: 'white',
  },
  qrCodeImage: {
    height: scale(75),
    width: scale(75),
  },
});

export default Footer;
