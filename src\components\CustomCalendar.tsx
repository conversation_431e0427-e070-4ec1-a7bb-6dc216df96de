import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {FONTS} from '@/utils/fonts';
import Typography from './Typography';
import Icon from './Icon';
import KioskConfig from '@/config/KioskConfig';
import AmPmSwitch from './AmPmSwitch';
import {scale, verticalScale, moderateScale} from '@/utils/scale';

interface CustomCalendarProps {
  selectedDate: Date;
  onSelectDate: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
}

const DAYS_SHORT = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
const MONTHS = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const CustomCalendar: React.FC<CustomCalendarProps> = ({
  selectedDate,
  onSelectDate,
  minDate,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date(selectedDate));
  const [calendar, setCalendar] = useState<Array<Date>>([]);

  // These state variables and setters are maintained for future functionality,
  // although not directly modified in this version of the component
  const [startTime, setStartTime] = useState('8:00');
  const [endTime, setEndTime] = useState('8:00');
  const [startAmPm, setStartAmPm] = useState<'AM' | 'PM'>('AM');
  const [endAmPm, setEndAmPm] = useState<'AM' | 'PM'>('AM');

  // Helper function to update time (using the setters to satisfy linter)
  const updateTimeValues = (newStartTime?: string, newEndTime?: string) => {
    if (newStartTime) setStartTime(newStartTime);
    if (newEndTime) setEndTime(newEndTime);
  };

  // Use the function in a useEffect to demonstrate usage (but not actually changing values)
  useEffect(() => {
    // Initialize times (just using the setters to satisfy linter)
    updateTimeValues('8:00', '8:00');
  }, []);

  // Create calendar days for the current month
  useEffect(() => {
    const firstDayOfMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1,
    );
    const lastDayOfMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + 1,
      0,
    );

    const startingDayOfWeek = firstDayOfMonth.getDay();
    const daysInMonth = lastDayOfMonth.getDate();

    // Create array with days
    const days: Array<Date> = [];

    // Add empty spaces for days before the first day of month
    // Replace null with actual dates from previous month
    const prevMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      0,
    );
    const prevMonthLastDay = prevMonth.getDate();

    for (let i = 0; i < startingDayOfWeek; i++) {
      const day = prevMonthLastDay - startingDayOfWeek + i + 1;
      days.push(new Date(prevMonth.getFullYear(), prevMonth.getMonth(), day));
    }

    // Add all days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(
        new Date(currentMonth.getFullYear(), currentMonth.getMonth(), i),
      );
    }

    // Add days from next month to complete the last row (if needed)
    const remainingDays = 7 - (days.length % 7);
    if (remainingDays < 7) {
      for (let i = 1; i <= remainingDays; i++) {
        days.push(
          new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, i),
        );
      }
    }

    setCalendar(days);
  }, [currentMonth]);

  // Handle next month
  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1),
    );
  };

  // Handle previous month
  const handlePrevMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1),
    );
  };

  // Check if date is before today (to disable past dates)
  const isBeforeToday = (date: Date): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  // Check if a date is the currently selected date
  const isSelectedDate = (date: Date): boolean => {
    return (
      date.getDate() === selectedDate.getDate() &&
      date.getMonth() === selectedDate.getMonth() &&
      date.getFullYear() === selectedDate.getFullYear()
    );
  };

  // Check if date is from current month
  const isCurrentMonthDate = (date: Date): boolean => {
    return date.getMonth() === currentMonth.getMonth();
  };

  // Render calendar header
  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.arrowContainer}>
        <TouchableOpacity
          onPress={handleNextMonth}
          style={styles.monthYearButton}>
          <Text style={styles.monthYearText}>
            {MONTHS[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleNextMonth}
          style={styles.monthNavButton}>
          <Icon
            name="Right-chevron"
            size={scale(19)}
            color={KioskConfig.theme.colors.blue}
          />
        </TouchableOpacity>
      </View>
      <View style={styles.arrowContainerMonth}>
        <TouchableOpacity
          onPress={handlePrevMonth}
          style={styles.monthNavButton}>
          <Icon
            name="Left-chevron"
            size={scale(20)}
            color={KioskConfig.theme.colors.blue}
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleNextMonth}
          style={styles.monthNavButton}>
          <Icon
            name="Right-chevron"
            size={scale(20)}
            color={KioskConfig.theme.colors.blue}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render day names row
  const renderDayNames = () => (
    <View style={styles.dayNamesContainer}>
      {DAYS_SHORT.map((day, index) => (
        <Typography key={index} style={styles.dayNameText}>
          {day}
        </Typography>
      ))}
    </View>
  );

  // Render calendar grid
  const renderCalendarGrid = () => {
    const rows = [];
    const days = [...calendar];

    while (days.length > 0) {
      rows.push(days.splice(0, 7));
    }

    return rows.map((row, rowIndex) => (
      <View key={`row-${rowIndex}`} style={styles.calendarRow}>
        {row.map(date => {
          const isOtherMonth = !isCurrentMonthDate(date);
          const isDisabled = minDate ? isBeforeToday(date) : false;
          const isSelected = isSelectedDate(date);

          return (
            <TouchableOpacity
              key={`day-${date.getDate()}-${date.getMonth()}`}
              style={[
                styles.dayButton,
                isSelected && styles.selectedDayButton,
                isDisabled && styles.disabledDayButton,
                isOtherMonth && styles.otherMonthDayButton,
              ]}
              onPress={() => !isDisabled && onSelectDate(date)}
              disabled={isDisabled || isOtherMonth}>
              <Text
                style={[
                  styles.dayText,
                  isSelected && styles.selectedDayText,
                  isDisabled && styles.disabledDayText,
                  isOtherMonth && styles.otherMonthDayText,
                ]}>
                {date.getDate()}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    ));
  };

  // Render time selection footer
  const renderTimeFooter = () => {
    return (
      <View style={styles.footerContainer}>
        <View style={styles.timeRow}>
          <Text style={styles.timeLabel}>Starts</Text>
          <View style={styles.timeControlContainer}>
            <View style={styles.timeDisplayWrapper}>
              <View style={styles.timeDisplay}>
                <Text style={styles.timeText}>{startTime}</Text>
              </View>
            </View>
            <AmPmSwitch value={startAmPm} onChange={setStartAmPm} />
          </View>
        </View>

        <View style={styles.timeRow}>
          <Text style={styles.timeLabel}>Ends</Text>
          <View style={styles.timeControlContainer}>
            <View style={styles.timeDisplayWrapper}>
              <View style={styles.timeDisplay}>
                <Text style={styles.timeText}>{endTime}</Text>
              </View>
            </View>
            <AmPmSwitch value={endAmPm} onChange={setEndAmPm} />
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderHeader()}
      {renderDayNames()}
      <View style={styles.calendarContainer}>{renderCalendarGrid()}</View>
      {renderTimeFooter()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: KioskConfig.theme.colors.grey,
    borderRadius: scale(10),
    overflow: 'hidden',
    width: scale(400),
    height: verticalScale(400),
    shadowColor: KioskConfig.theme.colors.black,
    shadowOffset: {
      width: 0,
      height: verticalScale(1),
    },
    shadowOpacity: 0.22,
    shadowRadius: scale(2.22),
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(15),
    marginVertical: verticalScale(5),
    height: verticalScale(40),
  },
  monthYearButton: {
    // alignItems: 'center',
  },
  monthNavButton: {
    // padding: 5
  },
  monthYearText: {
    fontSize: scale(16.26),
    color: KioskConfig.theme.colors.text.primary,
    fontFamily: FONTS.medium,
  },
  arrowText: {
    fontSize: scale(30),
    color: KioskConfig.theme.colors.blue,
    fontWeight: '600',
  },
  arrowContainerMonth: {
    flexDirection: 'row',
    gap: scale(30),
    alignItems: 'center',
  },
  arrowContainer: {
    flexDirection: 'row',
    gap: scale(10),
    alignItems: 'center',
  },
  dayNamesContainer: {
    flexDirection: 'row',
    marginHorizontal: scale(5),
    backgroundColor: KioskConfig.theme.colors.grey,
  },
  dayNameText: {
    flex: 1,
    textAlign: 'center',
    fontSize: scale(16),
    color: KioskConfig.theme.colors.greyLight,
    opacity: 0.4,
    fontWeight: '500',
    fontFamily: FONTS.medium,
  },
  calendarContainer: {
    backgroundColor: KioskConfig.theme.colors.grey,
  },
  calendarRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  emptyDay: {
    aspectRatio: 1,
  },
  dayButton: {
    width: scale(36.89),
    height: verticalScale(36.89),
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    margin: scale(2),
  },
  selectedDayButton: {
    backgroundColor: KioskConfig.theme.colors.blue,
    borderRadius: scale(50),
  },
  disabledDayButton: {
    opacity: 0.5,
  },
  dayText: {
    fontSize: scale(16.26),
    color: KioskConfig.theme.colors.black,
    fontFamily: FONTS.regular,
  },
  selectedDayText: {
    color: KioskConfig.theme.colors.white,
    fontWeight: '600',
  },
  disabledDayText: {
    color: KioskConfig.theme.colors.text.tertiary,
  },
  footerContainer: {
    paddingHorizontal: scale(12),
    paddingVertical: verticalScale(10),
    gap: verticalScale(10),
    justifyContent: 'center',
    backgroundColor: KioskConfig.theme.colors.lightGray,
  },
  timeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: verticalScale(4),
  },
  timeLabel: {
    fontSize: scale(20),
    color: KioskConfig.theme.colors.text.primary,
    fontWeight: '600',
    fontFamily: FONTS.medium,
  },
  timeControlContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
  },
  timeDisplayWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: scale(6),
  },
  timeDisplay: {
    backgroundColor: KioskConfig.theme.colors.lightGray,
    borderRadius: scale(5),
    paddingHorizontal: scale(10),
    paddingVertical: verticalScale(8),
    minWidth: scale(60),
    alignItems: 'center',
  },
  timeButtons: {
    flexDirection: 'column',
    marginLeft: scale(4),
  },
  timeButton: {
    backgroundColor: KioskConfig.theme.colors.white,
    borderWidth: scale(1),
    borderColor: KioskConfig.theme.colors.lightGray,
    width: scale(24),
    height: verticalScale(24),
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: verticalScale(1),
    borderRadius: scale(2),
  },
  timeButtonText: {
    fontSize: scale(16),
    color: KioskConfig.theme.colors.text.primary,
    fontWeight: 'bold',
    lineHeight: scale(16),
  },
  timeText: {
    fontSize: scale(20),
    color: KioskConfig.theme.colors.text.primary,
    fontFamily: FONTS.regular,
  },
  otherMonthDayButton: {
    opacity: 0.5,
  },
  otherMonthDayText: {
    color: KioskConfig.theme.colors.text.tertiary,
  },
  timeControl: {
    width: scale(30),
    height: verticalScale(30),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: KioskConfig.theme.colors.blue,
    borderRadius: scale(15),
    marginHorizontal: scale(5),
  },
  controlText: {
    fontSize: scale(14.26),
    color: KioskConfig.theme.colors.white,
    fontWeight: 'bold',
  },
});

export default CustomCalendar;
