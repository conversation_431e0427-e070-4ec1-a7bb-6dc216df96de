/* Custom theme for Kiosk Documentation - GoRaql style */

/* Main layout and colors */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8f9fa;
  color: #333;
  margin: 0;
  padding: 0;
}

/* Header styling */
.navbar {
  background-color: #fff;
  border-bottom: 1px solid #e1e4e8;
  padding: 1rem 2rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 600;
  color: #24292e;
  text-decoration: none;
}

/* Sidebar styling */
.sidebar {
  background-color: #fff;
  border-right: 1px solid #e1e4e8;
  width: 280px;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  overflow-y: auto;
  padding: 1rem 0;
  box-shadow: 1px 0 3px rgba(0,0,0,0.1);
}

.sidebar h3 {
  color: #586069;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 1.5rem 1rem 0.5rem;
  padding: 0;
}

.sidebar ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar li {
  margin: 0;
}

.sidebar a {
  display: block;
  padding: 0.5rem 1rem;
  color: #586069;
  text-decoration: none;
  font-size: 0.875rem;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
}

.sidebar a:hover {
  background-color: #f6f8fa;
  color: #0366d6;
  border-left-color: #0366d6;
}

.sidebar a.current {
  background-color: #f1f8ff;
  color: #0366d6;
  border-left-color: #0366d6;
  font-weight: 500;
}

/* Main content area */
.main-content {
  margin-left: 280px;
  padding: 2rem;
  max-width: calc(100% - 280px);
}

/* Page title */
h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #24292e;
  margin: 0 0 1rem 0;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 1rem;
}

h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #24292e;
  margin: 2rem 0 1rem 0;
  border-bottom: 1px solid #e1e4e8;
  padding-bottom: 0.5rem;
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #24292e;
  margin: 1.5rem 0 0.75rem 0;
}

/* Code blocks */
pre {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.45;
}

code {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.875em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* Tables */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

th, td {
  border: 1px solid #e1e4e8;
  padding: 0.75rem;
  text-align: left;
}

th {
  background-color: #f6f8fa;
  font-weight: 600;
}

/* Links */
a {
  color: #0366d6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Search box */
.search-box {
  margin: 1rem;
  padding: 0.5rem;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  width: calc(100% - 2rem);
  font-size: 0.875rem;
}

/* Note boxes */
.note {
  background-color: #f1f8ff;
  border: 1px solid #c8e1ff;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.note::before {
  content: "📝 Note: ";
  font-weight: 600;
  color: #0366d6;
}

/* Method signatures */
.signature {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.875rem;
}

/* Parameters table */
.params {
  margin: 1rem 0;
}

.params th {
  background-color: #f6f8fa;
  color: #24292e;
  font-weight: 600;
}

/* Examples */
.example {
  margin: 1rem 0;
}

.example h5 {
  color: #586069;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .main-content {
    margin-left: 0;
    max-width: 100%;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

/* Breadcrumbs */
.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
}

.breadcrumb a {
  color: #586069;
}

.breadcrumb a:hover {
  color: #0366d6;
}

/* Footer */
.footer {
  margin-top: 3rem;
  padding: 2rem 0;
  border-top: 1px solid #e1e4e8;
  color: #586069;
  font-size: 0.875rem;
  text-align: center;
}

/* Syntax highlighting improvements */
.hljs {
  background-color: #f6f8fa !important;
}

.hljs-keyword {
  color: #d73a49;
}

.hljs-string {
  color: #032f62;
}

.hljs-comment {
  color: #6a737d;
}

.hljs-function {
  color: #6f42c1;
}
