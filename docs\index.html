<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Home</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Home</h1>

    



    


    <h3> </h3>










    




    <section>
        <article><h1>Kiosk Application</h1>
<p>A React Native kiosk application optimized for 32-inch HD displays (1920x1080).</p>
<h2>Features</h2>
<ul>
<li>Fullscreen kiosk mode</li>
<li>Welcome screen with start button</li>
<li>Home screen with example options</li>
<li>Screen timeout prevention</li>
<li>Inactivity detection and reset</li>
<li>Hardware back button blocking (Android)</li>
<li>Optimized for 32&quot; HD displays</li>
</ul>
<h2>Setup Instructions</h2>
<h3>Prerequisites</h3>
<ul>
<li>Node.js (&gt;= 18.0.0)</li>
<li>Yarn or npm</li>
<li>React Native development environment</li>
<li>Android SDK for Android deployment</li>
<li>Xcode for iOS deployment</li>
</ul>
<h3>Installation</h3>
<ol>
<li>Clone the repository:</li>
</ol>
<pre class="prettyprint source lang-bash"><code>git clone https://github.com/yourusername/kiosk-app.git
cd kiosk-app
</code></pre>
<ol start="2">
<li>Install dependencies:</li>
</ol>
<pre class="prettyprint source lang-bash"><code>yarn install
# or
npm install
</code></pre>
<ol start="3">
<li>Install the required Android/iOS dependencies:</li>
</ol>
<pre class="prettyprint source lang-bash"><code># For iOS
cd ios && pod install && cd ..
</code></pre>
<h3>Code Style and Linting</h3>
<p>This project uses ESLint and Prettier for code quality and consistent formatting.</p>
<h4>Automatic Formatting on Save</h4>
<p>VSCode is configured to automatically format files and fix linting errors when you save:</p>
<ol>
<li>
<p>Install the recommended VSCode extensions:</p>
<ul>
<li>ESLint: <code>dbaeumer.vscode-eslint</code></li>
<li>Prettier: <code>esbenp.prettier-vscode</code></li>
</ul>
</li>
<li>
<p>ESLint errors will be automatically fixed and code will be formatted when you press Ctrl+S.</p>
</li>
</ol>
<h4>Manual Commands</h4>
<p>You can also run formatting and linting manually:</p>
<pre class="prettyprint source lang-bash"><code># Format all files with Prettier
yarn format

# Fix ESLint errors
yarn lint:fix

# Run both formatting and linting fixes
yarn fix-all
</code></pre>
<h3>Running the Application</h3>
<h4>Development Mode</h4>
<p>To run the application in development mode:</p>
<pre class="prettyprint source lang-bash"><code># Start the Metro server
yarn start

# Run on Android
yarn android

# Run on iOS
yarn ios
</code></pre>
<h4>Production Build</h4>
<p>To create a production build:</p>
<pre class="prettyprint source lang-bash"><code># For Android
yarn build:android

# The APK will be available at: android/app/build/outputs/apk/release/app-release.apk
</code></pre>
<h3>Cleaning Up and Distribution</h3>
<p>To clean the project of build artifacts and large files before sharing or archiving:</p>
<ol>
<li>Use the included cleanup script:</li>
</ol>
<pre class="prettyprint source lang-bash"><code># Make the script executable if needed
chmod +x clean.sh

# Run the cleanup script
./clean.sh
</code></pre>
<ol start="2">
<li>Or create a clean zip file:</li>
</ol>
<pre class="prettyprint source lang-bash"><code># Make the script executable if needed
chmod +x create-clean-zip.sh

# Create a clean zip file
./create-clean-zip.sh
</code></pre>
<p>The clean-up process removes:</p>
<ul>
<li>node_modules</li>
<li>lock files (yarn.lock, package-lock.json)</li>
<li>Android build artifacts (.so files, .dex files, build directories)</li>
<li>iOS build artifacts (build, Pods)</li>
<li>Other large temporary files</li>
</ul>
<p>This will significantly reduce the repository size from ~450MB to a few MB.</p>
<h2>Kiosk Mode Setup</h2>
<h3>Android</h3>
<p>To set up a device in kiosk mode:</p>
<ol>
<li>Install the APK on your device.</li>
<li>Enable Developer Options by tapping Build Number 7 times in the Settings &gt; About Phone menu.</li>
<li>Enable USB debugging in Developer Options.</li>
<li>Use Android Device Policy Controller (DPC) or a Mobile Device Management (MDM) solution to set the app as a kiosk app.</li>
</ol>
<p>Alternatively, on Android 9+, you can use the following ADB commands to enable kiosk mode:</p>
<pre class="prettyprint source lang-bash"><code># Replace com.yourcompany.kioskapp with your app's package name
adb shell dpm set-device-owner com.yourcompany.kioskapp/.DeviceAdminReceiver
</code></pre>
<h2>Configuration</h2>
<p>You can configure the kiosk behavior by modifying the <code>src/config/KioskConfig.ts</code> file:</p>
<ul>
<li>Display settings (resolution, orientation, status bar)</li>
<li>Application settings (name, default page, inactivity timeout)</li>
<li>Theme settings (colors, font sizes)</li>
</ul>
<h2>Customization</h2>
<h3>Adding New Screens</h3>
<ol>
<li>Create a new screen component in the <code>src/screens</code> directory.</li>
<li>Import it in <code>App.tsx</code>.</li>
<li>Add the necessary state and navigation logic in <code>App.tsx</code>.</li>
</ol>
<h3>Styling</h3>
<p>The application uses a centralized theme defined in <code>src/config/KioskConfig.ts</code>. Update this file to change the overall appearance of the app.</p>
<h2>License</h2>
<p>MIT</p></article>
    </section>






</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-Utils_SampleDocumented.html">Utils/SampleDocumented</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 4.0.4</a> on Fri Jul 04 2025 15:19:28 GMT+0530 (India Standard Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>