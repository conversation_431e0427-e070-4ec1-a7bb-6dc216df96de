/**
 * @fileoverview Navigation Types - TypeScript type definitions for React Navigation
 * @module Navigation/Types
 * @category Navigation
 */

/**
 * Main stack navigation parameter list.
 * Defines the screens available in the main navigation stack and their required parameters.
 *
 * @typedef {Object} MainStackParamList
 * @property {undefined} Home - Home screen (no parameters required)
 * @property {undefined} RentDemoBuy - Rent/Demo/Buy selection screen
 * @property {undefined} ReservedOrReturn - Reserved or return screen
 * @property {undefined} CheckOutOrReturnScreen - Check out or return screen
 * @property {undefined} OTPorQRScreen - OTP or QR code screen
 * @property {undefined} ConfirmScreen - Confirmation screen
 * @property {undefined} ModifyReservationScreen - Modify reservation screen
 * @property {undefined} AddToOrderScreen - Add to order screen
 * @property {undefined} AddSomthingElseScreen - Add something else screen
 *
 * @example
 * import { NavigationProp } from '@react-navigation/native';
 * import { MainStackParamList } from '@/navigation/types';
 *
 * type MainStackNavigationProp = NavigationProp<MainStackParamList>;
 *
 * // In a component
 * const navigation = useNavigation<MainStackNavigationProp>();
 * navigation.navigate('Home');
 */
export type MainStackParamList = {
  Home: undefined;
  RentDemoBuy: undefined;
  ReservedOrReturn: undefined;
  CheckOutOrReturnScreen: undefined;
  OTPorQRScreen: undefined;
  ConfirmScreen: undefined;
  ModifyReservationScreen: undefined;
  AddToOrderScreen: undefined;
  AddSomthingElseScreen: undefined;
};
