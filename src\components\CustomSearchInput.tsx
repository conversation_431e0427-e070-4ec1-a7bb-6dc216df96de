import React, {useState} from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Keyboard,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';
import Icon from '../components/Icon';
import {scale, verticalScale, moderateScale} from '@/utils/scale';

interface CustomSearchInputProps {
  placeholder?: string;
  onSearch?: (text: string) => void;
  btn?: object;
  setExpandedOption?: (expanded: boolean) => void;
}

const CustomSearchInput: React.FC<CustomSearchInputProps> = ({
  placeholder = 'Search...',
  onSearch,
  btn,
  setExpandedOption = () => {},
}) => {
  const [expanded, setExpanded] = useState(false);
  const [searchText, setSearchText] = useState('');

  const handlePress = () => {
    setExpanded(true);
    setExpandedOption(true);
  };

  const handleClose = () => {
    Keyboard.dismiss();
    setExpanded(false);
    setExpandedOption(false);
  };

  const handleSearch = (text: string) => {
    setSearchText(text);
    if (onSearch) {
      onSearch(text);
    }
  };

  return (
    <View style={styles.container}>
      {/* Collapsed search input */}
      {/* {!expanded && ( */}
      <TouchableOpacity
        style={[styles.collapsedInput, btn]}
        onPress={handlePress}>
        <View style={styles.searchIconContainer}>
          <Icon name="search-1" size={scale(50)} color="#383838" />
        </View>
      </TouchableOpacity>
      {/* )} */}

      {/* Modal with expanded search input */}
      <Modal
        visible={expanded}
        transparent={true}
        animationType="fade"
        onRequestClose={handleClose}>
        <TouchableWithoutFeedback onPress={handleClose}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
              <View style={styles.expandedInputContainer}>
                <TextInput
                  style={styles.expandedInput}
                  placeholder={placeholder}
                  value={searchText}
                  onChangeText={handleSearch}
                  autoFocus={true}
                  placeholderTextColor="#383838"
                />
                <Icon name="search-1" size={scale(44)} color="#383838" />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  collapsedInput: {
    minWidth: scale(596),
    height: verticalScale(104),
    borderRadius: scale(124),
    backgroundColor: '#D9D9D9D9',
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  searchIconContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: scale(36),
  },
  searchIcon: {
    height: scale(44),
    width: scale(44),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandedInputContainer: {
    width: width * 0.9,
    flexDirection: 'row',
    paddingVertical: verticalScale(10),
    alignItems: 'center',
    borderRadius: scale(124),
    backgroundColor: '#D9D9D9',
    paddingHorizontal: scale(36),
  },
  expandedInput: {
    flex: 1,
    height: verticalScale(104),
    fontSize: scale(48),
    lineHeight: scale(48),
    color: '#383838',
  },
  expandedSearchIcon: {
    height: scale(44),
    width: scale(44),
  },
});

export default CustomSearchInput;
