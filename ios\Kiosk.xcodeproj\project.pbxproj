// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0C80B921A6F3F58F76C31292 /* libPods-Kiosk.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5DCACB8F33CDC322A6C60F78 /* libPods-Kiosk.a */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		761780ED2CA45674006654EE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 761780EC2CA45674006654EE /* AppDelegate.swift */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		B4AB28D471FD466EBFEE4ADA /* HelveticaNeueLTStd-Md.otf in Resources */ = {isa = PBXBuildFile; fileRef = A3432C3475384351A7F204F2 /* HelveticaNeueLTStd-Md.otf */; };
		A363254A8686495799A50B5F /* Cocogoose-Classic-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4EFB87BB6F40437EA40A5D0D /* Cocogoose-Classic-Regular.ttf */; };
		A645DE6511144187B2922A56 /* icomoon.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 21DF2BEB7536412C8B31E058 /* icomoon.ttf */; };
		9231E271A0F24421BDBAA445 /* icomoon.eot in Resources */ = {isa = PBXBuildFile; fileRef = 716FF5382A634E9C9576A4A7 /* icomoon.eot */; };
		422C03A8AD5C418AA0DCD0C2 /* icomoon.svg in Resources */ = {isa = PBXBuildFile; fileRef = B6351BA8FE4B4181BDDE88EF /* icomoon.svg */; };
		5FF0430C57564B239666ACB4 /* icomoon.woff in Resources */ = {isa = PBXBuildFile; fileRef = E765EF70421242A0BC37273F /* icomoon.woff */; };
		608F09A16A624E798F452044 /* selection.json in Resources */ = {isa = PBXBuildFile; fileRef = 0F25C330272D493CAD54F5FA /* selection.json */; };
		5158E5B23F1E4C82AF1DEDEB /* CocogooseClassic-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 77DD60ACDFC646549E2BAAD4 /* CocogooseClassic-BlackItalic.ttf */; };
		FE43DA71D3CA4906B6E0E9E2 /* CocogooseClassic-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 54A63F5F57874E988243024D /* CocogooseClassic-Black.ttf */; };
		2CF3DE31BF1046BBAADD6BCB /* CocogooseClassic-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4FDCDCD933614CD2BEE5E7F2 /* CocogooseClassic-BoldItalic.ttf */; };
		01C925C14E234DDF8AB63F54 /* CocogooseClassic-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8436AC056F994535A1BEC856 /* CocogooseClassic-Bold.ttf */; };
		8787F257902841719B757BD5 /* CocogooseClassic-ExtraBoldIt.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D53CCC742F1942C2A177D24C /* CocogooseClassic-ExtraBoldIt.ttf */; };
		E616D352555C48D6A354E3E1 /* CocogooseClassic-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 48D61BCF492848AE90E712B6 /* CocogooseClassic-ExtraBold.ttf */; };
		16D9F53AF4194C118415B8C4 /* CocogooseClassic-ExtraLightIt.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 3F7B427538574B03A09BC38C /* CocogooseClassic-ExtraLightIt.ttf */; };
		3294995F93B24F329509E297 /* CocogooseClassic-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = A26F2B7F93EB43529CA41916 /* CocogooseClassic-ExtraLight.ttf */; };
		F550844B9F2C4DECB44C7B69 /* CocogooseClassic-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = C2652E92DE3A4116A4534630 /* CocogooseClassic-Italic.ttf */; };
		AEF39EB78123490982DFF388 /* CocogooseClassic-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 75D8C62064EE498D8A3CB49B /* CocogooseClassic-LightItalic.ttf */; };
		EB9EC52F045148F48B777EB9 /* CocogooseClassic-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BDC384D23FAE48EEB02151D7 /* CocogooseClassic-Light.ttf */; };
		F785F4C59ED74B33ADE96CB0 /* CocogooseClassic-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 733A9667CBFC497DB40F2BA4 /* CocogooseClassic-MediumItalic.ttf */; };
		F863E8EF4CE9497F90186F89 /* CocogooseClassic-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = BA7DD47D1EC04ECB889524CF /* CocogooseClassic-Medium.ttf */; };
		6A445E9E0E0D4D67B7252D0E /* CocogooseClassic-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC02C60DE89D44E8993E75B4 /* CocogooseClassic-Regular.ttf */; };
		50AB8D80475F46DE887114EE /* CocogooseClassic-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8D3E34D4EF4847438DA7677A /* CocogooseClassic-ThinItalic.ttf */; };
		76D819AAD1E14AEB88D25325 /* CocogooseClassic-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 59753B375B304AD0B1E49AD8 /* CocogooseClassic-Thin.ttf */; };
		69B325B1E3254173A61D2EFE /* HelveticaNeueLTStd-Bd.otf in Resources */ = {isa = PBXBuildFile; fileRef = FC30857E1A634B1DB75D7EEE /* HelveticaNeueLTStd-Bd.otf */; };
		D2A70B2D25E742898AA9D36B /* HelveticaNeueLTStd-Blk.otf in Resources */ = {isa = PBXBuildFile; fileRef = 98575D86E2A8470A90D8021D /* HelveticaNeueLTStd-Blk.otf */; };
		E3C146906E484419A36F25AE /* HelveticaNeueLTStd-Lt.otf in Resources */ = {isa = PBXBuildFile; fileRef = BD6D5F1074FE46A4A16E7FCF /* HelveticaNeueLTStd-Lt.otf */; };
		FE8BCC5D52414F03A2BAB1ED /* HelveticaNeueLTStd-Roman.otf in Resources */ = {isa = PBXBuildFile; fileRef = 163D7ABA7C5D47E1BD1EFA95 /* HelveticaNeueLTStd-Roman.otf */; };
		668C88C24B9846AFB3A63A48 /* HelveticaNeueLTStd-Th.otf in Resources */ = {isa = PBXBuildFile; fileRef = F902A48BDF7F4857942CA8E2 /* HelveticaNeueLTStd-Th.otf */; };
		C04F8E9F976043EBA2D77B43 /* README.md in Resources */ = {isa = PBXBuildFile; fileRef = AB927F89ED2E41649FDE3CC6 /* README.md */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = Kiosk;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* Kiosk.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Kiosk.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Kiosk/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Kiosk/Info.plist; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = Kiosk/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-Kiosk.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Kiosk.debug.xcconfig"; path = "Target Support Files/Pods-Kiosk/Pods-Kiosk.debug.xcconfig"; sourceTree = "<group>"; };
		5709B34CF0A7D63546082F79 /* Pods-Kiosk.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Kiosk.release.xcconfig"; path = "Target Support Files/Pods-Kiosk/Pods-Kiosk.release.xcconfig"; sourceTree = "<group>"; };
		5DCACB8F33CDC322A6C60F78 /* libPods-Kiosk.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Kiosk.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		761780EC2CA45674006654EE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = Kiosk/AppDelegate.swift; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = Kiosk/LaunchScreen.storyboard; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		A3432C3475384351A7F204F2 /* HelveticaNeueLTStd-Md.otf */ = {isa = PBXFileReference; name = "HelveticaNeueLTStd-Md.otf"; path = "../src/assest/fonts/HelveticaNeueLTStd-Md.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		4EFB87BB6F40437EA40A5D0D /* Cocogoose-Classic-Regular.ttf */ = {isa = PBXFileReference; name = "Cocogoose-Classic-Regular.ttf"; path = "../src/assest/fonts/Cocogoose-Classic-Regular.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		21DF2BEB7536412C8B31E058 /* icomoon.ttf */ = {isa = PBXFileReference; name = "icomoon.ttf"; path = "../src/assest/fonts/icomoon/icomoon.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		716FF5382A634E9C9576A4A7 /* icomoon.eot */ = {isa = PBXFileReference; name = "icomoon.eot"; path = "../src/assest/fonts/icomoon/icomoon.eot"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		B6351BA8FE4B4181BDDE88EF /* icomoon.svg */ = {isa = PBXFileReference; name = "icomoon.svg"; path = "../src/assest/fonts/icomoon/icomoon.svg"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		E765EF70421242A0BC37273F /* icomoon.woff */ = {isa = PBXFileReference; name = "icomoon.woff"; path = "../src/assest/fonts/icomoon/icomoon.woff"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		0F25C330272D493CAD54F5FA /* selection.json */ = {isa = PBXFileReference; name = "selection.json"; path = "../src/assest/fonts/icomoon/selection.json"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		77DD60ACDFC646549E2BAAD4 /* CocogooseClassic-BlackItalic.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-BlackItalic.ttf"; path = "../src/assest/fonts/CocogooseClassic-BlackItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		54A63F5F57874E988243024D /* CocogooseClassic-Black.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Black.ttf"; path = "../src/assest/fonts/CocogooseClassic-Black.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		4FDCDCD933614CD2BEE5E7F2 /* CocogooseClassic-BoldItalic.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-BoldItalic.ttf"; path = "../src/assest/fonts/CocogooseClassic-BoldItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		8436AC056F994535A1BEC856 /* CocogooseClassic-Bold.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Bold.ttf"; path = "../src/assest/fonts/CocogooseClassic-Bold.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		D53CCC742F1942C2A177D24C /* CocogooseClassic-ExtraBoldIt.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-ExtraBoldIt.ttf"; path = "../src/assest/fonts/CocogooseClassic-ExtraBoldIt.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		48D61BCF492848AE90E712B6 /* CocogooseClassic-ExtraBold.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-ExtraBold.ttf"; path = "../src/assest/fonts/CocogooseClassic-ExtraBold.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		3F7B427538574B03A09BC38C /* CocogooseClassic-ExtraLightIt.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-ExtraLightIt.ttf"; path = "../src/assest/fonts/CocogooseClassic-ExtraLightIt.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		A26F2B7F93EB43529CA41916 /* CocogooseClassic-ExtraLight.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-ExtraLight.ttf"; path = "../src/assest/fonts/CocogooseClassic-ExtraLight.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		C2652E92DE3A4116A4534630 /* CocogooseClassic-Italic.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Italic.ttf"; path = "../src/assest/fonts/CocogooseClassic-Italic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		75D8C62064EE498D8A3CB49B /* CocogooseClassic-LightItalic.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-LightItalic.ttf"; path = "../src/assest/fonts/CocogooseClassic-LightItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		BDC384D23FAE48EEB02151D7 /* CocogooseClassic-Light.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Light.ttf"; path = "../src/assest/fonts/CocogooseClassic-Light.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		733A9667CBFC497DB40F2BA4 /* CocogooseClassic-MediumItalic.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-MediumItalic.ttf"; path = "../src/assest/fonts/CocogooseClassic-MediumItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		BA7DD47D1EC04ECB889524CF /* CocogooseClassic-Medium.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Medium.ttf"; path = "../src/assest/fonts/CocogooseClassic-Medium.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		CC02C60DE89D44E8993E75B4 /* CocogooseClassic-Regular.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Regular.ttf"; path = "../src/assest/fonts/CocogooseClassic-Regular.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		8D3E34D4EF4847438DA7677A /* CocogooseClassic-ThinItalic.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-ThinItalic.ttf"; path = "../src/assest/fonts/CocogooseClassic-ThinItalic.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		59753B375B304AD0B1E49AD8 /* CocogooseClassic-Thin.ttf */ = {isa = PBXFileReference; name = "CocogooseClassic-Thin.ttf"; path = "../src/assest/fonts/CocogooseClassic-Thin.ttf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		FC30857E1A634B1DB75D7EEE /* HelveticaNeueLTStd-Bd.otf */ = {isa = PBXFileReference; name = "HelveticaNeueLTStd-Bd.otf"; path = "../src/assest/fonts/HelveticaNeueLTStd-Bd.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		98575D86E2A8470A90D8021D /* HelveticaNeueLTStd-Blk.otf */ = {isa = PBXFileReference; name = "HelveticaNeueLTStd-Blk.otf"; path = "../src/assest/fonts/HelveticaNeueLTStd-Blk.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		BD6D5F1074FE46A4A16E7FCF /* HelveticaNeueLTStd-Lt.otf */ = {isa = PBXFileReference; name = "HelveticaNeueLTStd-Lt.otf"; path = "../src/assest/fonts/HelveticaNeueLTStd-Lt.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		163D7ABA7C5D47E1BD1EFA95 /* HelveticaNeueLTStd-Roman.otf */ = {isa = PBXFileReference; name = "HelveticaNeueLTStd-Roman.otf"; path = "../src/assest/fonts/HelveticaNeueLTStd-Roman.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		F902A48BDF7F4857942CA8E2 /* HelveticaNeueLTStd-Th.otf */ = {isa = PBXFileReference; name = "HelveticaNeueLTStd-Th.otf"; path = "../src/assest/fonts/HelveticaNeueLTStd-Th.otf"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
		AB927F89ED2E41649FDE3CC6 /* README.md */ = {isa = PBXFileReference; name = "README.md"; path = "../src/assest/fonts/README.md"; sourceTree = "<group>"; fileEncoding = undefined; lastKnownFileType = unknown; explicitFileType = undefined; includeInIndex = 0; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0C80B921A6F3F58F76C31292 /* libPods-Kiosk.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* Kiosk */ = {
			isa = PBXGroup;
			children = (
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				761780EC2CA45674006654EE /* AppDelegate.swift */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
			);
			name = Kiosk;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				5DCACB8F33CDC322A6C60F78 /* libPods-Kiosk.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* Kiosk */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				221C289793B84EF6A624A84D /* Resources */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* Kiosk.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B4392A12AC88292D35C810B /* Pods-Kiosk.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-Kiosk.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		221C289793B84EF6A624A84D /* Resources */ = {
			isa = "PBXGroup";
			children = (
				A3432C3475384351A7F204F2 /* HelveticaNeueLTStd-Md.otf */,
				4EFB87BB6F40437EA40A5D0D /* Cocogoose-Classic-Regular.ttf */,
				21DF2BEB7536412C8B31E058 /* icomoon.ttf */,
				716FF5382A634E9C9576A4A7 /* icomoon.eot */,
				B6351BA8FE4B4181BDDE88EF /* icomoon.svg */,
				E765EF70421242A0BC37273F /* icomoon.woff */,
				0F25C330272D493CAD54F5FA /* selection.json */,
				77DD60ACDFC646549E2BAAD4 /* CocogooseClassic-BlackItalic.ttf */,
				54A63F5F57874E988243024D /* CocogooseClassic-Black.ttf */,
				4FDCDCD933614CD2BEE5E7F2 /* CocogooseClassic-BoldItalic.ttf */,
				8436AC056F994535A1BEC856 /* CocogooseClassic-Bold.ttf */,
				D53CCC742F1942C2A177D24C /* CocogooseClassic-ExtraBoldIt.ttf */,
				48D61BCF492848AE90E712B6 /* CocogooseClassic-ExtraBold.ttf */,
				3F7B427538574B03A09BC38C /* CocogooseClassic-ExtraLightIt.ttf */,
				A26F2B7F93EB43529CA41916 /* CocogooseClassic-ExtraLight.ttf */,
				C2652E92DE3A4116A4534630 /* CocogooseClassic-Italic.ttf */,
				75D8C62064EE498D8A3CB49B /* CocogooseClassic-LightItalic.ttf */,
				BDC384D23FAE48EEB02151D7 /* CocogooseClassic-Light.ttf */,
				733A9667CBFC497DB40F2BA4 /* CocogooseClassic-MediumItalic.ttf */,
				BA7DD47D1EC04ECB889524CF /* CocogooseClassic-Medium.ttf */,
				CC02C60DE89D44E8993E75B4 /* CocogooseClassic-Regular.ttf */,
				8D3E34D4EF4847438DA7677A /* CocogooseClassic-ThinItalic.ttf */,
				59753B375B304AD0B1E49AD8 /* CocogooseClassic-Thin.ttf */,
				FC30857E1A634B1DB75D7EEE /* HelveticaNeueLTStd-Bd.otf */,
				98575D86E2A8470A90D8021D /* HelveticaNeueLTStd-Blk.otf */,
				BD6D5F1074FE46A4A16E7FCF /* HelveticaNeueLTStd-Lt.otf */,
				163D7ABA7C5D47E1BD1EFA95 /* HelveticaNeueLTStd-Roman.otf */,
				F902A48BDF7F4857942CA8E2 /* HelveticaNeueLTStd-Th.otf */,
				AB927F89ED2E41649FDE3CC6 /* README.md */,
			);
			name = Resources;
			sourceTree = "<group>";
			path = "";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* Kiosk */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Kiosk" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				FFA4A87B596446468043A1B5 /* Upload Debug Symbols to Sentry */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Kiosk;
			productName = Kiosk;
			productReference = 13B07F961A680F5B00A75B9A /* Kiosk.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Kiosk" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Kiosk */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				B4AB28D471FD466EBFEE4ADA /* HelveticaNeueLTStd-Md.otf in Resources */,
				A363254A8686495799A50B5F /* Cocogoose-Classic-Regular.ttf in Resources */,
				A645DE6511144187B2922A56 /* icomoon.ttf in Resources */,
				9231E271A0F24421BDBAA445 /* icomoon.eot in Resources */,
				422C03A8AD5C418AA0DCD0C2 /* icomoon.svg in Resources */,
				5FF0430C57564B239666ACB4 /* icomoon.woff in Resources */,
				608F09A16A624E798F452044 /* selection.json in Resources */,
				5158E5B23F1E4C82AF1DEDEB /* Cocogoose-Classic-Black-Italic.otf in Resources */,
				FE43DA71D3CA4906B6E0E9E2 /* Cocogoose-Classic-Black.otf in Resources */,
				2CF3DE31BF1046BBAADD6BCB /* Cocogoose-Classic-Bold-Italic.otf in Resources */,
				01C925C14E234DDF8AB63F54 /* Cocogoose-Classic-Bold.otf in Resources */,
				8787F257902841719B757BD5 /* Cocogoose-Classic-Extra-Bold-Italic.otf in Resources */,
				E616D352555C48D6A354E3E1 /* Cocogoose-Classic-Extra-Bold.otf in Resources */,
				16D9F53AF4194C118415B8C4 /* Cocogoose-Classic-Extra-Light-Italic.otf in Resources */,
				3294995F93B24F329509E297 /* Cocogoose-Classic-Extra-Light.otf in Resources */,
				F550844B9F2C4DECB44C7B69 /* Cocogoose-Classic-Italic.otf in Resources */,
				AEF39EB78123490982DFF388 /* Cocogoose-Classic-Light-Italic.otf in Resources */,
				EB9EC52F045148F48B777EB9 /* Cocogoose-Classic-Light.otf in Resources */,
				F785F4C59ED74B33ADE96CB0 /* Cocogoose-Classic-Medium-Italic.otf in Resources */,
				F863E8EF4CE9497F90186F89 /* Cocogoose-Classic-Medium.otf in Resources */,
				6A445E9E0E0D4D67B7252D0E /* Cocogoose-Classic-Regular.otf in Resources */,
				50AB8D80475F46DE887114EE /* Cocogoose-Classic-Thin-Italic.otf in Resources */,
				76D819AAD1E14AEB88D25325 /* Cocogoose-Classic-Thin.otf in Resources */,
				69B325B1E3254173A61D2EFE /* HelveticaNeueLTStd-Bd.otf in Resources */,
				D2A70B2D25E742898AA9D36B /* HelveticaNeueLTStd-Blk.otf in Resources */,
				E3C146906E484419A36F25AE /* HelveticaNeueLTStd-Lt.otf in Resources */,
				FE8BCC5D52414F03A2BAB1ED /* HelveticaNeueLTStd-Roman.otf in Resources */,
				668C88C24B9846AFB3A63A48 /* HelveticaNeueLTStd-Th.otf in Resources */,
				C04F8E9F976043EBA2D77B43 /* README.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		00EEFC60759A1932668264C0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Kiosk/Pods-Kiosk-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Kiosk/Pods-Kiosk-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Kiosk/Pods-Kiosk-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Kiosk-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Kiosk/Pods-Kiosk-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Kiosk/Pods-Kiosk-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Kiosk/Pods-Kiosk-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FFA4A87B596446468043A1B5 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			name = "Upload Debug Symbols to Sentry";
			inputPaths = (
			);
			outputPaths = (
			);
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				761780ED2CA45674006654EE /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* Kiosk */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B4392A12AC88292D35C810B /* Pods-Kiosk.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Kiosk/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = Kiosk;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5709B34CF0A7D63546082F79 /* Pods-Kiosk.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = Kiosk/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = Kiosk;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Kiosk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Kiosk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
