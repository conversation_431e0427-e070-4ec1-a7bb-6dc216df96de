import React from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {MainStackParamList} from '@/navigation';
import {gripSizesData} from '@/config/staticData';
import FastImage from 'react-native-fast-image';
import {scale, verticalScale} from '@/utils/scale';
import {useLanguageStore} from '@/store/languageStore';

interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

export type ProductOptionsProps = {
  route: RouteProp<MainStackParamList, 'ProductOptions'>;
};

interface ItemProps {
  title: string;
  description: string;
  image: number;
  color: {value: string; label: string}[];
}
const ProductOptions = ({route}: ProductOptionsProps) => {
  const {item, from}: {item: ItemProps; from: string} = route.params || {};
  console.log('item=====>>>>>', item);
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();

  // Ensure item is available before navigation
  const handleNavigation = (selectedOption: string) => {
    if (!item) return;

    navigation.navigate('ProductOptionSelected', {
      from: from || '',
      selectedOption,
      item,
    });
  };

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: 'Try it now',
      onPress: () => {
        handleNavigation('TryNow');
      },
    },
    {
      id: 2,
      title: 'Reserve',
      onPress: () => {
        handleNavigation('Reserve');
      },
    },
    {
      id: 3,
      title: 'Purchase',
      onPress: () => {
        handleNavigation('Purchase');
      },
    },
  ];

  const color = [
    {
      value: '#0050A4',
      label: '#0050A4',
    },
  ];

  const {t} = useLanguageStore();

  const renderBallDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            title={t(item?.title || '')}
            description={t(item?.description || '')}
            actionButtons={actionButtons}
            radioTitle="Select one"
            counterTitle="Quantity"
            gripSizes={gripSizesData}
            colors={item?.color || color}
          />
        </View>
        <View style={styles.imageContainer}>
          <FastImage
            source={item?.image}
            style={styles.image}
            resizeMode="contain"
          />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderBallDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    marginHorizontal: 'auto',
    flexDirection: 'row',
    position: 'relative',
    flex: 1,
  },
  contentContainer: {
    width: scale(489),
    height: verticalScale(1050),
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: scale(100),
    top: verticalScale(100),
  },
  image: {
    width: scale(353),
    height: verticalScale(961),
  },
});
export default ProductOptions;
