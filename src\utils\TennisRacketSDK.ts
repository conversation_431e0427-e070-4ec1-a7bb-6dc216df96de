import { NativeModules, NativeEventEmitter, EmitterSubscription } from 'react-native';

const { TennisRacketModule } = NativeModules;

if (!TennisRacketModule) {
  throw new Error('TennisRacketModule is not available. Make sure the native module is properly linked.');
}

// Create event emitter for SDK events
const tennisRacketEventEmitter = new NativeEventEmitter(TennisRacketModule);

// Process status event name
const PROCESS_STATUS_EVENT = 'racketProcessStatus';
const RACKET_INFO_EVENT = 'racketInfo';

// Process status types
export enum ProcessStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PROCESSING = 'PROCESSING',
  TIMEOUT = 'TIMEOUT',
}

// Model interfaces
export interface RacketInfo {
  id: string;
  name: string;
  status: string;
  brand: string;
  weight: string;
}

export interface CabinetInfo {
  id: string;
  name: string;
  isOccupied: boolean;
  racketId: string;
}

export interface CabinetStatus {
  id: string;
  isOccupied: boolean;
  racketId: string;
}

export interface MachineStatus {
  isOperational: boolean;
  statusMessage: string;
  errorCode: number;
}

export interface BallInfo {
  type: string;
  quantity: number;
  available: boolean;
}

export interface LockerInfo {
  id: string;
  name: string;
  isOccupied: boolean;
  userId?: string;
}

// Event types
export interface ProcessStatusEvent {
  type: 'processStatus';
  status: ProcessStatus;
  message: string;
}

export interface RacketInfoEvent {
  type: 'racketInfo';
  id: string;
  name: string;
  status?: string;
  brand?: string;
  weight?: string;
}

export type TennisRacketEvent = ProcessStatusEvent | RacketInfoEvent;

/**
 * Tennis Racket Machine SDK Interface
 */
class TennisRacketSDK {
  private eventListeners: EmitterSubscription[] = [];
  private initialized: boolean = false;

  /**
   * Initialize the SDK logs
   * @returns Promise that resolves when log initialization is complete
   */
  async initializeLogs(): Promise<boolean> {
    try {
      return await TennisRacketModule.initializeLogs();
    } catch (error) {
      console.error('Failed to initialize Tennis Racket SDK logs:', error);
      throw error;
    }
  }

  /**
   * Initialize the serial port
   * @returns Promise that resolves when serial port initialization is complete
   */
  async initializeSerialPort(): Promise<boolean> {
    try {
      const result = await TennisRacketModule.initializeSerialPort();
      this.initialized = result;
      return result;
    } catch (error) {
      console.error('Failed to initialize Tennis Racket SDK serial port:', error);
      throw error;
    }
  }

  /**
   * Create a result listener
   * @returns Promise that resolves when the listener is created
   */
  async createResultListener(): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.createResultListener();
    } catch (error) {
      console.error('Failed to create Tennis Racket SDK result listener:', error);
      throw error;
    }
  }

  /**
   * Open a cabinet
   * @param cabinetId ID of the cabinet to open
   * @returns Promise that resolves when the command is sent
   */
  async openCabinet(cabinetId: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.openCabinet(cabinetId);
    } catch (error) {
      console.error(`Failed to open cabinet ${cabinetId}:`, error);
      throw error;
    }
  }

  /**
   * Return a racket to a cabinet
   * @param cabinetId ID of the cabinet
   * @param racketId ID of the racket
   * @returns Promise that resolves when the command is sent
   */
  async returnRacket(cabinetId: string, racketId: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.returnRacket(cabinetId, racketId);
    } catch (error) {
      console.error(`Failed to return racket ${racketId} to cabinet ${cabinetId}:`, error);
      throw error;
    }
  }

  /**
   * Borrow a racket from a cabinet
   * @param cabinetId ID of the cabinet
   * @returns Promise that resolves when the command is sent
   */
  async borrowRacket(cabinetId: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.borrowRacket(cabinetId);
    } catch (error) {
      console.error(`Failed to borrow racket from cabinet ${cabinetId}:`, error);
      throw error;
    }
  }

  /**
   * Get information about a racket
   * @param racketId ID of the racket
   * @returns Promise that resolves with the racket information
   */
  async getRacketInfo(racketId: string): Promise<RacketInfo> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.getRacketInfo(racketId);
    } catch (error) {
      console.error(`Failed to get racket info for ${racketId}:`, error);
      throw error;
    }
  }

  /**
   * Get all available rackets
   * @returns Promise that resolves with an array of racket information
   */
  async getAllRackets(): Promise<RacketInfo[]> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.getAllRackets();
    } catch (error) {
      console.error('Failed to get all rackets:', error);
      throw error;
    }
  }

  /**
   * Get all cabinets
   * @returns Promise that resolves with an array of cabinet information
   */
  async getAllCabinets(): Promise<CabinetInfo[]> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.getAllCabinets();
    } catch (error) {
      console.error('Failed to get all cabinets:', error);
      throw error;
    }
  }

  /**
   * Get cabinet status
   * @param cabinetId ID of the cabinet
   * @returns Promise that resolves with the cabinet status
   */
  async getCabinetStatus(cabinetId: string): Promise<CabinetStatus> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.getCabinetStatus(cabinetId);
    } catch (error) {
      console.error(`Failed to get cabinet status for ${cabinetId}:`, error);
      throw error;
    }
  }

  /**
   * Reset the machine
   * @returns Promise that resolves when the command is sent
   */
  async resetMachine(): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.resetMachine();
    } catch (error) {
      console.error('Failed to reset machine:', error);
      throw error;
    }
  }

  /**
   * Dispense balls
   * @param ballType Type of ball to dispense
   * @param quantity Number of balls to dispense
   * @returns Promise that resolves when the command is sent
   */
  async dispenseBalls(ballType: string, quantity: number): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      if (!this.hasListener) {
        throw new Error('Event listener not created. Call createResultListener first.');
      }
      return await TennisRacketModule.dispenseBalls(ballType, quantity);
    } catch (error) {
      console.error(`Failed to dispense ${quantity} balls of type ${ballType}:`, error);
      throw error;
    }
  }

  /**
   * Get available ball types
   * @returns Promise that resolves with an array of ball information
   */
  async getAvailableBalls(): Promise<BallInfo[]> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.getAvailableBalls();
    } catch (error) {
      console.error('Failed to get available balls:', error);
      throw error;
    }
  }

  /**
   * Open locker
   * @param lockerId ID of the locker to open
   * @returns Promise that resolves when the command is sent
   */
  async openLocker(lockerId: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      if (!this.hasListener) {
        throw new Error('Event listener not created. Call createResultListener first.');
      }
      return await TennisRacketModule.openLocker(lockerId);
    } catch (error) {
      console.error(`Failed to open locker ${lockerId}:`, error);
      throw error;
    }
  }

  /**
   * Get all lockers
   * @returns Promise that resolves with an array of locker information
   */
  async getAllLockers(): Promise<LockerInfo[]> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.getAllLockers();
    } catch (error) {
      console.error('Failed to get all lockers:', error);
      throw error;
    }
  }

  /**
   * Assign locker to user
   * @param lockerId ID of the locker to assign
   * @param userId ID of the user to assign the locker to
   * @returns Promise that resolves when the command is sent
   */
  async assignLocker(lockerId: string, userId: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.assignLocker(lockerId, userId);
    } catch (error) {
      console.error(`Failed to assign locker ${lockerId} to user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Release locker from user
   * @param lockerId ID of the locker to release
   * @returns Promise that resolves when the command is sent
   */
  async releaseLocker(lockerId: string): Promise<boolean> {
    try {
      if (!this.initialized) {
        throw new Error('SDK not initialized. Call initializeSerialPort first.');
      }
      return await TennisRacketModule.releaseLocker(lockerId);
    } catch (error) {
      console.error(`Failed to release locker ${lockerId}:`, error);
      throw error;
    }
  }

  /**
   * Add an event listener for process status events
   * @param callback Function to call when a process status event occurs
   * @returns Subscription that can be used to remove the listener
   */
  addProcessStatusListener(callback: (event: ProcessStatusEvent) => void): EmitterSubscription {
    // Register the event with the native module
    TennisRacketModule.addListener(PROCESS_STATUS_EVENT);

    // Add the JS event listener
    const subscription = tennisRacketEventEmitter.addListener(PROCESS_STATUS_EVENT, callback);
    this.eventListeners.push(subscription);

    return subscription;
  }

  /**
   * Add an event listener for racket info events
   * @param callback Function to call when a racket info event occurs
   * @returns Subscription that can be used to remove the listener
   */
  addRacketInfoListener(callback: (event: RacketInfoEvent) => void): EmitterSubscription {
    // Register the event with the native module
    TennisRacketModule.addListener(RACKET_INFO_EVENT);

    // Add the JS event listener
    const subscription = tennisRacketEventEmitter.addListener(RACKET_INFO_EVENT, callback);
    this.eventListeners.push(subscription);

    return subscription;
  }

  /**
   * Remove all event listeners
   */
  removeAllListeners(): void {
    this.eventListeners.forEach(listener => listener.remove());
    this.eventListeners = [];
    TennisRacketModule.removeListeners(0);
  }
}

// Export a singleton instance
export default new TennisRacketSDK();
