<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Source: sampleDocumented.js</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Source: sampleDocumented.js</h1>

    



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>/**
 * @fileoverview Sample documented utility functions for JSDoc testing
 * @module Utils/SampleDocumented
 * @category Utils
 */

/**
 * Calculates the sum of two numbers
 * @function add
 * @param {number} a - The first number
 * @param {number} b - The second number
 * @returns {number} The sum of a and b
 * @example
 * const result = add(5, 3); // returns 8
 */
export const add = (a, b) => {
  return a + b;
};

/**
 * Formats a string with proper capitalization
 * @function formatString
 * @param {string} str - The string to format
 * @returns {string} The formatted string
 * @example
 * const formatted = formatString('hello world'); // returns 'Hello World'
 */
export const formatString = (str) => {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Configuration object for the sample module
 * @constant {Object}
 * @property {string} version - The module version
 * @property {boolean} debug - Whether debug mode is enabled
 */
export const CONFIG = {
  version: '1.0.0',
  debug: false
};

/**
 * @typedef {Object} User
 * @property {string} id - User unique identifier
 * @property {string} name - User display name
 * @property {string} email - User email address
 * @property {boolean} [active=true] - Whether the user is active
 */

/**
 * Creates a new user object
 * @function createUser
 * @param {string} name - The user's name
 * @param {string} email - The user's email
 * @returns {User} The created user object
 * @example
 * const user = createUser('John Doe', '<EMAIL>');
 */
export const createUser = (name, email) => {
  return {
    id: Math.random().toString(36).substr(2, 9),
    name,
    email,
    active: true
  };
};
</code></pre>
        </article>
    </section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Modules</h3><ul><li><a href="module-Utils_SampleDocumented.html">Utils/SampleDocumented</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 4.0.4</a> on Fri Jul 04 2025 15:19:28 GMT+0530 (India Standard Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>
