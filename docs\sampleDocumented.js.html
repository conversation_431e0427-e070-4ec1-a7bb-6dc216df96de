

<!DOCTYPE html>
<html lang="en">

<head>
  
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Kiosk App - React Native Documentation sampleDocumented.js</title>

  <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="./build/entry.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
  <![endif]-->
  <link href="https://fonts.googleapis.com/css?family=Roboto:100,400,700|Inconsolata,700" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
  <link type="text/css" rel="stylesheet" href="https://jmblog.github.io/color-themes-for-google-code-prettify/themes/tomorrow-night.min.css">
  <link type="text/css" rel="stylesheet" href="styles/app.min.css">
  <link type="text/css" rel="stylesheet" href="styles/iframe.css">
  <link type="text/css" rel="stylesheet" href="styles/style.css">
  <script async defer src="https://buttons.github.io/buttons.js"></script>

  
</head>



<body class="layout small-header">
    <div id="stickyNavbarOverlay"></div>
    

<div class="top-nav">
    <div class="inner">
        <a id="hamburger" role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
            <span aria-hidden="true"></span>
        </a>
        <div class="logo">
            
             
                <a href="index.html">
                    <h1 class="navbar-item">Kiosk Documentation</h1>
                </a>
            
        </div>
        <div class="menu">
            
            <div class="navigation">
                <a
                    href="index.html"
                    class="link"
                >
                    Documentation
                </a>
                
                 
                    
                        <a
                            class="link user-link "
                            href="#components"
                        >
                            Components
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#screens"
                        >
                            Screens
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#utils"
                        >
                            Utils
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#services"
                        >
                            Services
                        </a>
                    
                        <a
                            class="link user-link "
                            href="#hooks"
                        >
                            Hooks
                        </a>
                    
                
                
            </div>
        </div>
    </div>
</div>
    <div id="main">
        <div
            class="sidebar "
            id="sidebarNav"
        >
            
                <div class="search-wrapper">
                    <input id="search" type="text" placeholder="Search docs..." class="input">
                </div>
            
            <nav>
                
                    <h2><a href="index.html">Documentation</a></h2><div class="category"><h3>Modules</h3><ul><li><a href="module-Utils_SampleDocumented.html">Utils/SampleDocumented</a></li></ul></div>
                
            </nav>
        </div>
        <div class="core" id="main-content-wrapper">
            <div class="content">
                <header class="page-title">
                    <p>Source</p>
                    <h1>sampleDocumented.js</h1>
                </header>
                



    
    <section>
        <article>
            <pre class="prettyprint source linenums"><code>/**
 * @fileoverview Sample documented utility functions for JSDoc testing
 * @module Utils/SampleDocumented
 * @category Utils
 */

/**
 * Calculates the sum of two numbers
 * @function add
 * @param {number} a - The first number
 * @param {number} b - The second number
 * @returns {number} The sum of a and b
 * @example
 * const result = add(5, 3); // returns 8
 */
export const add = (a, b) => {
  return a + b;
};

/**
 * Formats a string with proper capitalization
 * @function formatString
 * @param {string} str - The string to format
 * @returns {string} The formatted string
 * @example
 * const formatted = formatString('hello world'); // returns 'Hello World'
 */
export const formatString = (str) => {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Configuration object for the sample module
 * @constant {Object}
 * @property {string} version - The module version
 * @property {boolean} debug - Whether debug mode is enabled
 */
export const CONFIG = {
  version: '1.0.0',
  debug: false
};

/**
 * @typedef {Object} User
 * @property {string} id - User unique identifier
 * @property {string} name - User display name
 * @property {string} email - User email address
 * @property {boolean} [active=true] - Whether the user is active
 */

/**
 * Creates a new user object
 * @function createUser
 * @param {string} name - The user's name
 * @param {string} email - The user's email
 * @returns {User} The created user object
 * @example
 * const user = createUser('John Doe', '<EMAIL>');
 */
export const createUser = (name, email) => {
  return {
    id: Math.random().toString(36).substr(2, 9),
    name,
    email,
    active: true
  };
};
</code></pre>
        </article>
    </section>




            </div>
            
        </div>
        <div id="side-nav" class="side-nav">
        </div>
    </div>
<script src="scripts/app.min.js"></script>
<script>PR.prettyPrint();</script>
<script src="scripts/linenumber.js"> </script>

<script src="scripts/search.js"> </script>


</body>
</html>
