import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  SharedValue,
} from 'react-native-reanimated';
import {scale} from '@/utils/scale';
import FastImage from 'react-native-fast-image';

const CARD_WIDTH = scale(300);
const CARD_GAP = 20;

interface GalleryCarouselItemProps {
  item: {
    image: string;
    ar: number; // aspect ratio
  };
  index: number;
  scrollXOffset: SharedValue<number>;
  _scrollViewWidth: number; // Renamed with underscore to indicate it's not used
  handleCarouselItemPress: (index: number) => void;
}

const GalleryCarouselItem: React.FC<GalleryCarouselItemProps> = ({
  item,
  index,
  scrollXOffset,
  _scrollViewWidth, // Renamed with underscore to indicate it's not used
  handleCarouselItemPress,
}) => {
  // Create animated style for the card
  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    // Calculate input range directly inside the worklet function
    const position = -scrollXOffset.value;
    const inputRange = [
      (index - 1) * (CARD_WIDTH + CARD_GAP), // Previous card
      index * (CARD_WIDTH + CARD_GAP), // Current card
      (index + 1) * (CARD_WIDTH + CARD_GAP), // Next card
    ];

    // Scale animation
    const scaleValue = interpolate(position, inputRange, [0.9, 1, 0.9]);

    // Opacity animation
    const opacity = interpolate(position, inputRange, [0.7, 1, 0.7]);

    return {
      transform: [{scale: scaleValue}],
      opacity,
    };
  });

  // Calculate image height based on aspect ratio
  const imageHeight = CARD_WIDTH * (1 / (item.ar || 1));

  return (
    <TouchableOpacity
      activeOpacity={0.9}
      onPress={() => handleCarouselItemPress(index)}
      style={styles.container}>
      <Animated.View style={[styles.card, animatedStyle]}>
        <FastImage
          source={{uri: item.image}}
          style={[
            styles.image,
            {
              height: imageHeight,
            },
          ]}
          resizeMode="cover"
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CARD_WIDTH,
    paddingHorizontal: scale(10),
  },
  card: {
    borderRadius: scale(16),
    overflow: 'hidden',
    backgroundColor: '#fff',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  image: {
    width: '100%',
    borderRadius: scale(16),
  },
});

export default GalleryCarouselItem;
