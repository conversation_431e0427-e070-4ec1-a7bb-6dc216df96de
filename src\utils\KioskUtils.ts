/**
 * Kiosk Utilities
 *
 * Utility functions for kiosk application.
 */

import {BackHandler, Platform, AppState, AppStateStatus} from 'react-native';
import KioskConfig from '../config/KioskConfig';

/**
 * Timer for inactivity detection
 */
let inactivityTimer: number | null = null;

/**
 * Locks the device in kiosk mode by preventing
 * hardware back button and other escape mechanisms
 */
export const lockKioskMode = () => {
  // Prevent hardware back button (Android)
  if (Platform.OS === 'android') {
    BackHandler.addEventListener('hardwareBackPress', () => {
      // Block the back button in kiosk mode
      return true;
    });
  }

  // Prevent screen timeout
  if (Platform.OS === 'android') {
    // On Android, this would typically use NativeModules to keep screen on
    // This requires additional native code
    // eslint-disable-next-line no-console
    console.log('Screen timeout prevented');
  }
};

/**
 * Sets up the inactivity timeout that resets the app to
 * welcome screen after a period of inactivity
 *
 * @param resetCallback Function to call when inactivity timeout is reached
 */
export const setupInactivityTimeout = (resetCallback: () => void) => {
  if (KioskConfig.app.inactivityTimeout === 0) {
    // No timeout if set to 0
    return;
  }

  const resetInactivityTimer = () => {
    // Clear existing timer if any
    if (inactivityTimer !== null) {
      clearTimeout(inactivityTimer);
    }

    // Set new timer
    inactivityTimer = setTimeout(() => {
      resetCallback();
    }, KioskConfig.app.inactivityTimeout) as unknown as number;
  };

  // Set up app state change listener
  AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      resetInactivityTimer();
    }
  });

  // Initialize the timer
  resetInactivityTimer();

  // Return a function to reset the timer on user interaction
  return {
    resetInactivityTimer,
  };
};

export default {
  lockKioskMode,
  setupInactivityTimeout,
};
