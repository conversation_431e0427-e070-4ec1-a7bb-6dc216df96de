{"tags": {"allowUnknownTags": true, "dictionaries": ["jsdoc", "closure"]}, "source": {"include": ["./src/", "./App.tsx", "./index.js"], "includePattern": "\\.(js|jsx|ts|tsx)$", "exclude": ["node_modules/", "android/", "ios/", "__tests__/", "*.test.js", "*.test.ts", "*.test.tsx", "*.spec.js", "*.spec.ts", "*.spec.tsx"]}, "opts": {"destination": "./docs/", "recurse": true, "readme": "./README.md"}, "plugins": ["plugins/markdown", "jsdoc-babel", "better-docs/typescript", "better-docs/category"], "templates": {"cleverLinks": false, "monospaceLinks": false, "better-docs": {"name": "Kiosk App Documentation", "title": "Kiosk App - React Native Documentation", "hideGenerator": true, "navigation": [{"label": "GitHub", "href": "https://github.com/your-repo/kiosk"}], "navLinks": [{"label": "Components", "href": "/components"}, {"label": "Screens", "href": "/screens"}, {"label": "Utils", "href": "/utils"}, {"label": "Services", "href": "/services"}, {"label": "<PERSON>s", "href": "/hooks"}]}}, "babel": {"extensions": ["js", "jsx", "ts", "tsx"], "ignore": ["**/*.test.js", "**/*.test.ts", "**/*.test.tsx"], "babelrc": false, "presets": [["@babel/preset-env", {"targets": {"node": "current"}}], "@babel/preset-typescript", "@babel/preset-react"], "plugins": ["@babel/plugin-transform-class-properties", "@babel/plugin-transform-object-rest-spread"]}}