package com.kiosk.tennisracket

import android.os.Environment
import android.util.Log
import com.elvishew.xlog.LogConfiguration
import com.elvishew.xlog.LogLevel
import com.elvishew.xlog.XLog
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import java.io.File
import java.lang.Exception

// Import these classes if they exist, otherwise use mock implementations
// This approach makes the module more robust when the SDK is not available
// or when testing on different devices
class TennisRacketSDK {
    companion object {
        fun getInstance(): TennisRacketSDK = TennisRacketSDK()
    }

    // Initialize the serial port
    fun initSerialPort(port: String) {
        Log.d("TennisRacketSDK", "Initializing serial port: $port")
    }

    // Cabinet operations
    fun openCabinet(cabinetId: String, callback: Any?) {
        Log.d("TennisRacketSDK", "Opening cabinet: $cabinetId")
    }

    fun returnRacket(cabinetId: String, racketId: String, callback: Any?) {
        Log.d("TennisRacketSDK", "Returning racket $racketId to cabinet: $cabinetId")
    }

    fun borrowRacket(cabinetId: String, callback: Any?) {
        Log.d("TennisRacketSDK", "Borrowing racket from cabinet: $cabinetId")
    }

    // Racket information
    fun getRacketInfo(racketId: String): RacketInfo? {
        Log.d("TennisRacketSDK", "Getting racket info for: $racketId")
        return RacketInfo(racketId, "Demo Racket")
    }

    // Get all available rackets
    fun getAllRackets(): List<RacketInfo> {
        Log.d("TennisRacketSDK", "Getting all available rackets")
        return listOf(
            RacketInfo("R001", "Demo Racket 1"),
            RacketInfo("R002", "Demo Racket 2"),
            RacketInfo("R003", "Demo Racket 3")
        )
    }

    // Get all cabinets
    fun getAllCabinets(): List<CabinetInfo> {
        Log.d("TennisRacketSDK", "Getting all cabinets")
        return listOf(
            CabinetInfo("1", "Cabinet 1", true),
            CabinetInfo("2", "Cabinet 2", false),
            CabinetInfo("3", "Cabinet 3", true)
        )
    }

    // Get cabinet status
    fun getCabinetStatus(cabinetId: String): CabinetStatus? {
        Log.d("TennisRacketSDK", "Getting status for cabinet: $cabinetId")
        return CabinetStatus(cabinetId, true, "R001")
    }

    // Check if a racket is available
    fun isRacketAvailable(racketId: String): Boolean {
        Log.d("TennisRacketSDK", "Checking if racket is available: $racketId")
        return true
    }

    // Ball vending operations
    fun dispenseBalls(ballType: String, quantity: Int, callback: Any?) {
        Log.d("TennisRacketSDK", "Dispensing $quantity balls of type $ballType")
    }

    // Get available ball types
    fun getAvailableBalls(): List<BallInfo> {
        Log.d("TennisRacketSDK", "Getting available ball types")
        return listOf(
            BallInfo("Standard", 100, true),
            BallInfo("Premium", 50, true),
            BallInfo("Training", 75, true)
        )
    }

    // Locker operations
    fun openLocker(lockerId: String, callback: Any?) {
        Log.d("TennisRacketSDK", "Opening locker: $lockerId")
    }

    fun getAllLockers(): List<LockerInfo> {
        Log.d("TennisRacketSDK", "Getting all lockers")
        return listOf(
            LockerInfo("L001", "Locker 1", false),
            LockerInfo("L002", "Locker 2", true, "U001"),
            LockerInfo("L003", "Locker 3", false)
        )
    }

    fun assignLocker(lockerId: String, userId: String): Boolean {
        Log.d("TennisRacketSDK", "Assigning locker $lockerId to user $userId")
        return true
    }

    fun releaseLocker(lockerId: String): Boolean {
        Log.d("TennisRacketSDK", "Releasing locker $lockerId")
        return true
    }

    // Reset the machine
    fun resetMachine(callback: Any?) {
        Log.d("TennisRacketSDK", "Resetting machine")
    }

    // Close the serial port
    fun closeSerialPort() {
        Log.d("TennisRacketSDK", "Closing serial port")
    }
}

// Model classes for the SDK
class RacketInfo(
    val id: String,
    val name: String,
    val status: String = "Available",
    val brand: String = "Unknown",
    val weight: String = "Medium"
)

class CabinetInfo(
    val id: String,
    val name: String,
    val isOccupied: Boolean,
    val racketId: String? = null
)

class CabinetStatus(
    val id: String,
    val isOccupied: Boolean,
    val racketId: String?
)

class MachineStatus(
    val isOperational: Boolean,
    val statusMessage: String,
    val errorCode: Int
)

class BallInfo(
    val type: String,
    val quantity: Int,
    val available: Boolean
)

class LockerInfo(
    val id: String,
    val name: String,
    val isOccupied: Boolean,
    val userId: String? = null
)

enum class ProcessStatus {
    SUCCESS, FAILED, PROCESSING, TIMEOUT
}

interface TennisRacketCallback {
    fun onProcessStatus(status: ProcessStatus, message: String)
    fun onRacketInfo(racketInfo: RacketInfo)
}

/**
 * React Native module for the Tennis Racket Machine SDK
 */
class TennisRacketModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val TAG = "TennisRacketModule"

    // Reference to the SDK instance
    private var sdkInstance: TennisRacketSDK? = null

    // Callback for SDK operations
    private var racketCallback: TennisRacketCallback? = null

    // Initialize the SDK when the module is created
    init {
        try {
            Log.d(TAG, "Initializing TennisRacketModule")
            sdkInstance = TennisRacketSDK.getInstance()
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing TennisRacketModule", e)
        }
    }

    override fun getName(): String {
        return "TennisRacketModule"
    }

    /**
     * Initialize the SDK logs
     */
    @ReactMethod
    fun initializeLogs(promise: Promise) {
        try {
            // Initialize XLog for SDK logging
            val config = LogConfiguration.Builder()
                .logLevel(LogLevel.ALL)
                .build()

            val logPath = Environment.getExternalStorageDirectory().absolutePath + File.separator + "tennis_racket_logs"
            XLog.init(config, null)

            Log.d(TAG, "Tennis Racket SDK logs initialized successfully at: $logPath")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Tennis Racket SDK logs", e)
            promise.reject("LOG_INIT_ERROR", "Failed to initialize logs: ${e.message}")
        }
    }

    /**
     * Initialize the serial port
     */
    @ReactMethod
    fun initializeSerialPort(promise: Promise) {
        try {
            // Make sure SDK instance is created
            if (sdkInstance == null) {
                sdkInstance = TennisRacketSDK.getInstance()
            }

            // Initialize the serial port
            sdkInstance?.initSerialPort("/dev/ttyS0")

            // Create a callback for SDK operations
            racketCallback = object : TennisRacketCallback {
                override fun onProcessStatus(status: ProcessStatus, message: String) {
                    val params = Arguments.createMap().apply {
                        putString("type", "processStatus")
                        putString("status", status.name)
                        putString("message", message)
                    }
                    sendEvent("racketProcessStatus", params)
                }

                override fun onRacketInfo(racketInfo: RacketInfo) {
                    val params = Arguments.createMap().apply {
                        putString("type", "racketInfo")
                        putString("id", racketInfo.id)
                        putString("name", racketInfo.name)
                    }
                    sendEvent("racketInfo", params)
                }
            }

            Log.d(TAG, "Tennis Racket SDK serial port initialized successfully")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Tennis Racket SDK serial port", e)
            promise.reject("SERIAL_INIT_ERROR", "Failed to initialize serial port: ${e.message}")
        }
    }

    /**
     * Create a result listener - now combined with initializeSerialPort for simplicity
     */
    @ReactMethod
    fun createResultListener(promise: Promise) {
        try {
            if (racketCallback == null) {
                // Create a callback for SDK operations
                racketCallback = object : TennisRacketCallback {
                    override fun onProcessStatus(status: ProcessStatus, message: String) {
                        val params = Arguments.createMap().apply {
                            putString("type", "processStatus")
                            putString("status", status.name)
                            putString("message", message)
                        }
                        sendEvent("racketProcessStatus", params)
                    }

                    override fun onRacketInfo(racketInfo: RacketInfo) {
                        val params = Arguments.createMap().apply {
                            putString("type", "racketInfo")
                            putString("id", racketInfo.id)
                            putString("name", racketInfo.name)
                        }
                        sendEvent("racketInfo", params)
                    }
                }
            }

            Log.d(TAG, "Tennis Racket SDK result listener created successfully")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create Tennis Racket SDK result listener", e)
            promise.reject("LISTENER_ERROR", "Failed to create result listener: ${e.message}")
        }
    }

    /**
     * Send an event to JavaScript
     */
    private fun sendEvent(eventName: String, params: WritableMap?) {
        try {
            reactApplicationContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit(eventName, params)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending event to JavaScript", e)
        }
    }

    /**
     * Open cabinet
     */
    @ReactMethod
    fun openCabinet(cabinetId: String, promise: Promise) {
        try {
            ensureInitialized()

            sdkInstance?.openCabinet(cabinetId, racketCallback)

            Log.d(TAG, "Open cabinet command sent for cabinet: $cabinetId")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open cabinet: $cabinetId", e)
            promise.reject("OPEN_CABINET_ERROR", "Failed to open cabinet: ${e.message}")
        }
    }

    /**
     * Return racket
     */
    @ReactMethod
    fun returnRacket(cabinetId: String, racketId: String, promise: Promise) {
        try {
            ensureInitialized()

            sdkInstance?.returnRacket(cabinetId, racketId, racketCallback)

            Log.d(TAG, "Return racket command sent for cabinet: $cabinetId, racket: $racketId")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to return racket to cabinet: $cabinetId", e)
            promise.reject("RETURN_RACKET_ERROR", "Failed to return racket: ${e.message}")
        }
    }

    /**
     * Borrow racket
     */
    @ReactMethod
    fun borrowRacket(cabinetId: String, promise: Promise) {
        try {
            ensureInitialized()

            sdkInstance?.borrowRacket(cabinetId, racketCallback)

            Log.d(TAG, "Borrow racket command sent for cabinet: $cabinetId")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to borrow racket from cabinet: $cabinetId", e)
            promise.reject("BORROW_RACKET_ERROR", "Failed to borrow racket: ${e.message}")
        }
    }

    /**
     * Get racket info
     */
    @ReactMethod
    fun getRacketInfo(racketId: String, promise: Promise) {
        try {
            ensureInitialized()

            val racketInfo = sdkInstance?.getRacketInfo(racketId)

            if (racketInfo != null) {
                val result = Arguments.createMap().apply {
                    putString("id", racketInfo.id)
                    putString("name", racketInfo.name)
                    putString("status", racketInfo.status)
                    putString("brand", racketInfo.brand)
                    putString("weight", racketInfo.weight)
                }
                promise.resolve(result)
            } else {
                promise.reject("RACKET_INFO_ERROR", "Racket info not found for ID: $racketId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get racket info for ID: $racketId", e)
            promise.reject("RACKET_INFO_ERROR", "Failed to get racket info: ${e.message}")
        }
    }

    /**
     * Get all available rackets
     */
    @ReactMethod
    fun getAllRackets(promise: Promise) {
        try {
            ensureInitialized()

            val rackets = sdkInstance?.getAllRackets()

            if (rackets != null && rackets.isNotEmpty()) {
                val result = Arguments.createArray()

                rackets.forEach { racket ->
                    val racketMap = Arguments.createMap().apply {
                        putString("id", racket.id)
                        putString("name", racket.name)
                        putString("status", racket.status)
                        putString("brand", racket.brand)
                        putString("weight", racket.weight)
                    }
                    result.pushMap(racketMap)
                }

                promise.resolve(result)
            } else {
                promise.resolve(Arguments.createArray())
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get all rackets", e)
            promise.reject("GET_ALL_RACKETS_ERROR", "Failed to get all rackets: ${e.message}")
        }
    }

    /**
     * Get all cabinets
     */
    @ReactMethod
    fun getAllCabinets(promise: Promise) {
        try {
            ensureInitialized()

            val cabinets = sdkInstance?.getAllCabinets()

            if (cabinets != null && cabinets.isNotEmpty()) {
                val result = Arguments.createArray()

                cabinets.forEach { cabinet ->
                    val cabinetMap = Arguments.createMap().apply {
                        putString("id", cabinet.id)
                        putString("name", cabinet.name)
                        putBoolean("isOccupied", cabinet.isOccupied)
                        putString("racketId", cabinet.racketId ?: "")
                    }
                    result.pushMap(cabinetMap)
                }

                promise.resolve(result)
            } else {
                promise.resolve(Arguments.createArray())
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get all cabinets", e)
            promise.reject("GET_ALL_CABINETS_ERROR", "Failed to get all cabinets: ${e.message}")
        }
    }

    /**
     * Get cabinet status
     */
    @ReactMethod
    fun getCabinetStatus(cabinetId: String, promise: Promise) {
        try {
            ensureInitialized()

            val cabinetStatus = sdkInstance?.getCabinetStatus(cabinetId)

            if (cabinetStatus != null) {
                val result = Arguments.createMap().apply {
                    putString("id", cabinetStatus.id)
                    putBoolean("isOccupied", cabinetStatus.isOccupied)
                    putString("racketId", cabinetStatus.racketId ?: "")
                }
                promise.resolve(result)
            } else {
                promise.reject("CABINET_STATUS_ERROR", "Cabinet status not found for ID: $cabinetId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get cabinet status for ID: $cabinetId", e)
            promise.reject("CABINET_STATUS_ERROR", "Failed to get cabinet status: ${e.message}")
        }
    }

    /**
     * Reset the machine
     */
    @ReactMethod
    fun resetMachine(promise: Promise) {
        try {
            ensureInitialized()

            sdkInstance?.resetMachine(racketCallback)

            Log.d(TAG, "Reset machine command sent")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to reset machine", e)
            promise.reject("RESET_MACHINE_ERROR", "Failed to reset machine: ${e.message}")
        }
    }

    /**
     * Dispense balls
     */
    @ReactMethod
    fun dispenseBalls(ballType: String, quantity: Int, promise: Promise) {
        try {
            ensureInitialized()

            sdkInstance?.dispenseBalls(ballType, quantity, racketCallback)

            Log.d(TAG, "Dispense balls command sent for type: $ballType, quantity: $quantity")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to dispense balls", e)
            promise.reject("DISPENSE_BALLS_ERROR", "Failed to dispense balls: ${e.message}")
        }
    }

    /**
     * Get available balls
     */
    @ReactMethod
    fun getAvailableBalls(promise: Promise) {
        try {
            ensureInitialized()

            val balls = sdkInstance?.getAvailableBalls()

            if (balls != null && balls.isNotEmpty()) {
                val result = Arguments.createArray()

                balls.forEach { ball ->
                    val ballMap = Arguments.createMap().apply {
                        putString("type", ball.type)
                        putInt("quantity", ball.quantity)
                        putBoolean("available", ball.available)
                    }
                    result.pushMap(ballMap)
                }

                promise.resolve(result)
            } else {
                promise.resolve(Arguments.createArray())
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get available balls", e)
            promise.reject("GET_AVAILABLE_BALLS_ERROR", "Failed to get available balls: ${e.message}")
        }
    }

    /**
     * Open locker
     */
    @ReactMethod
    fun openLocker(lockerId: String, promise: Promise) {
        try {
            ensureInitialized()

            sdkInstance?.openLocker(lockerId, racketCallback)

            Log.d(TAG, "Open locker command sent for locker: $lockerId")
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open locker: $lockerId", e)
            promise.reject("OPEN_LOCKER_ERROR", "Failed to open locker: ${e.message}")
        }
    }

    /**
     * Get all lockers
     */
    @ReactMethod
    fun getAllLockers(promise: Promise) {
        try {
            ensureInitialized()

            val lockers = sdkInstance?.getAllLockers()

            if (lockers != null && lockers.isNotEmpty()) {
                val result = Arguments.createArray()

                lockers.forEach { locker ->
                    val lockerMap = Arguments.createMap().apply {
                        putString("id", locker.id)
                        putString("name", locker.name)
                        putBoolean("isOccupied", locker.isOccupied)
                        putString("userId", locker.userId ?: "")
                    }
                    result.pushMap(lockerMap)
                }

                promise.resolve(result)
            } else {
                promise.resolve(Arguments.createArray())
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get all lockers", e)
            promise.reject("GET_ALL_LOCKERS_ERROR", "Failed to get all lockers: ${e.message}")
        }
    }

    /**
     * Assign locker to user
     */
    @ReactMethod
    fun assignLocker(lockerId: String, userId: String, promise: Promise) {
        try {
            ensureInitialized()

            val result = sdkInstance?.assignLocker(lockerId, userId)

            if (result == true) {
                Log.d(TAG, "Locker $lockerId assigned to user $userId")
                promise.resolve(true)
            } else {
                promise.reject("ASSIGN_LOCKER_ERROR", "Failed to assign locker $lockerId to user $userId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to assign locker $lockerId to user $userId", e)
            promise.reject("ASSIGN_LOCKER_ERROR", "Failed to assign locker: ${e.message}")
        }
    }

    /**
     * Release locker
     */
    @ReactMethod
    fun releaseLocker(lockerId: String, promise: Promise) {
        try {
            ensureInitialized()

            val result = sdkInstance?.releaseLocker(lockerId)

            if (result == true) {
                Log.d(TAG, "Locker $lockerId released")
                promise.resolve(true)
            } else {
                promise.reject("RELEASE_LOCKER_ERROR", "Failed to release locker $lockerId")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to release locker $lockerId", e)
            promise.reject("RELEASE_LOCKER_ERROR", "Failed to release locker: ${e.message}")
        }
    }

    /**
     * Helper method to ensure the SDK is initialized
     */
    private fun ensureInitialized() {
        if (sdkInstance == null) {
            sdkInstance = TennisRacketSDK.getInstance()
        }

        if (racketCallback == null) {
            racketCallback = object : TennisRacketCallback {
                override fun onProcessStatus(status: ProcessStatus, message: String) {
                    val params = Arguments.createMap().apply {
                        putString("type", "processStatus")
                        putString("status", status.name)
                        putString("message", message)
                    }
                    sendEvent("racketProcessStatus", params)
                }

                override fun onRacketInfo(racketInfo: RacketInfo) {
                    val params = Arguments.createMap().apply {
                        putString("type", "racketInfo")
                        putString("id", racketInfo.id)
                        putString("name", racketInfo.name)
                    }
                    sendEvent("racketInfo", params)
                }
            }
        }
    }

    /**
     * Add event listeners for SDK events
     */
    @ReactMethod
    fun addListener(eventName: String) {
        // This method is required for React Native event emitter setup
        Log.d(TAG, "Added listener for event: $eventName")
    }

    /**
     * Remove event listeners
     */
    @ReactMethod
    fun removeListeners(count: Int) {
        // This method is required for React Native event emitter cleanup
        Log.d(TAG, "Removed listeners")
    }

    /**
     * Clean up resources when the module is destroyed
     */
    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()

        try {
            // Clean up SDK resources
            sdkInstance?.closeSerialPort()
            sdkInstance = null
            racketCallback = null

            Log.d(TAG, "Tennis Racket SDK resources released")
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up SDK resources", e)
        }
    }
}
