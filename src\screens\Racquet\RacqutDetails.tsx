import React from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '@/navigation';
import {gripSizesData} from '@/config/staticData';
import ProductImage from '@/components/ProductImage';
import {StackNavigationProp} from '@react-navigation/stack';
import {scale, verticalScale} from '@/utils/scale';
import images from '@/config/images';
import {useLanguageStore} from '@/store/languageStore';

interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

const RacquetDetailsScreen = ({
  route,
}: {
  route: RouteProp<MainStackParamList, 'RacquetDetails'>;
}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();

  const {racquetData, brand} = route.params || {};
  console.log('racquetData=====>>>>>', racquetData);

  const actionButtons: ActionButton[] = [
    {
      id: 1,
      title: t('racquetDetailsActionButtons.tryItNow'),
      onPress: () => {
        if (racquetData) {
          navigation.navigate('TryItNowRacquetDetails', {
            racquetData: racquetData,
            brand: brand,
          });
        }
      },
    },
    {
      id: 2,
      title: t('racquetDetailsActionButtons.reserve'),
      onPress: () => {
        if (racquetData) {
          navigation.navigate('ProductOptionSelected', {
            from: 'racquet',
            selectedOption: 'Reserve',
            item: {
              ...racquetData,
              handleImage: images.dunlopCx200Handle,
            },
            type: 'brandList',
            brand: brand,
          });
        }
      },
    },
    {
      id: 3,
      title: t('racquetDetailsActionButtons.purchase'),
      onPress: () => {
        if (racquetData) {
          navigation.navigate('PurchaseRacquetDetails', {
            racquetData: racquetData,
          });
        }
      },
    },
  ];

  const renderRacquetDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            title={t(racquetData?.title || '')}
            description={t(racquetData?.description || '')}
            actionButtons={actionButtons}
            gripSizes={gripSizesData}
            colors={racquetData?.colors}
            helpMeChoosePress={() => navigation.navigate('HelpMeChooseRaquet')}
            brand={brand}
          />
        </View>
        <View style={styles.imageContainer}>
          <ProductImage
            image={racquetData?.image || 0}
            imageStyle={styles.image}
          />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>{renderRacquetDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    marginHorizontal: 'auto',
    flexDirection: 'row',
    position: 'relative',
    flex: 1,
  },
  contentContainer: {
    width: scale(489),
    height: verticalScale(1050),
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: scale(100),
    top: verticalScale(100),
  },
  image: {
    width: scale(353),
    height: verticalScale(961),
  },
});
export default RacquetDetailsScreen;
