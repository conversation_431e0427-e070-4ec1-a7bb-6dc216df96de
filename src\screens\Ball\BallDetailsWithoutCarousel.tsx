import React from 'react';
import GradientBackground from '../../components/GradientBackground';
import {StyleSheet, View} from 'react-native';
import ProductDetails from '@/components/Dunlop/ProductDetails';
import {RouteProp, useNavigation} from '@react-navigation/native';
import {MainStackParamList} from '@/navigation';
import {StackNavigationProp} from '@react-navigation/stack';
import {scale, verticalScale} from '@/utils/scale';
import BrandedBallsCategory from '@/components/BrandedBallsCatogory';
import {useCart} from '@/context/CartContext';
import {useLanguageStore} from '@/store/languageStore';
interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
}

const BallDetailsWithoutCarousel = ({
  route,
}: {
  route: RouteProp<MainStackParamList, 'BallDetailsWithoutCarousel'>;
}) => {
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  const {ballData, type} = route.params || {};
  const {addItem, items} = useCart();
  console.log('items=====>>>>>', items);

  const actionButtons: ActionButton[] = [
    ...(type === 'ball_by_type'
      ? [
          {
            id: 1,
            title: 'Add to Cart',
            onPress: () => {
              addItem({
                id: ballData.id.toString(),
                name: ballData.title,
                price: 0,
                quantity: 1,
              });
            },
          },
        ]
      : []),
    {
      id: type === 'ball_by_type' ? 2 : 1,
      title: 'Checkout',
      onPress: () => {
        navigation.navigate('Timer');
      },
    },
    {
      id: type === 'ball_by_type' ? 3 : 2,
      title: 'Continue shopping',
      onPress: () => {
        navigation.navigate('RentDemoBuy');
      },
    },
  ];

  const radioOptions = [
    {label: 'ATP Extra Duty', value: 'atp_extra_duty'},
    {label: 'ATP Regular Duty', value: 'atp_regular_duty'},
  ];

  const renderRacquetDetails = () => {
    return (
      <View style={styles.container}>
        <View style={[styles.contentContainer]}>
          <ProductDetails
            title={t(ballData?.title)}
            description={t(ballData?.description)}
            actionButtons={actionButtons}
            showCounter={true}
            counterTitle="Quantity"
            radioOptions={radioOptions}
            radioTitle="Select one"
          />
        </View>
        <View style={styles.imageContainer}>
          <BrandedBallsCategory
            image={ballData?.image}
            ballImage={ballData?.ballImage}
            showAddIcon={false}
            style={styles.image}
            ballImageStyle={styles.ballImage}
            ballImageContainerStyle={styles.ballImageContainer}
          />
        </View>
      </View>
    );
  };

  return (
    <GradientBackground cart={type === 'ball_by_type'}>
      <View style={styles.container}>{renderRacquetDetails()}</View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    maxWidth: scale(1190),
    marginHorizontal: 'auto',
    flexDirection: 'row',
    position: 'relative',
    flex: 1,
  },
  contentContainer: {
    width: scale(489),
    height: verticalScale(1050),
    paddingBottom: scale(200),
  },
  imageContainer: {
    flex: 1,
    position: 'absolute',
    right: scale(100),
    top: scale(0),
  },
  image: {
    width: scale(298),
    height: verticalScale(831),
  },
  ballImage: {
    width: 283,
    height: 280,
  },
  ballImageContainer: {
    position: 'absolute',
    right: '-40%',
    bottom: '-7%',
  },
});
export default BallDetailsWithoutCarousel;
