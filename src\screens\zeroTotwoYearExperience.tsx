import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import {MainStackParamList} from '@/navigation';
import {RouteProp} from '@react-navigation/native';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React from 'react';
import {useLanguageStore} from '@/store/languageStore';
export type zeroTotwoYearExperienceProps = {
  route: RouteProp<MainStackParamList, 'zeroTotwoYearExperience'>;
};

const ZeroTotwoYearExperience = ({route}: zeroTotwoYearExperienceProps) => {
  const {from} = route.params || {};
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        description={t('ZeroTotwoYearExperience.description')}
        btnText1={{
          text: t('ZeroTotwoYearExperience.btnText1_1'),
          text2: t('ZeroTotwoYearExperience.btnText1_2'),
        }}
        btnText2={{
          text: t('ZeroTotwoYearExperience.btnText2_1'),
          text2: '',
        }}
        onPress={() => {
          navigation.navigate('slowAndSteady', {
            from: from,
          });
        }}
        onPress2={() => {
          navigation.navigate('workoutWarrior', {
            from: from,
          });
        }}
      />
    </GradientBackground>
  );
};

export default ZeroTotwoYearExperience;
