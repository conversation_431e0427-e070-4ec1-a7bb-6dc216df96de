#!/bin/bash

# <PERSON>ript to clean the repository of build artifacts and large files

echo "Cleaning repository..."

# Clean node modules
echo "Removing node_modules..."
rm -rf node_modules

# Clean yarn and npm lock files
echo "Removing lock files..."
rm -f yarn.lock package-lock.json

# Clean Android build artifacts
echo "Cleaning Android build artifacts..."
rm -rf android/app/build
rm -rf android/build
rm -rf android/.gradle
rm -rf android/app/.cxx
find . -name "*.so" -type f -delete
find . -name "*.dex" -type f -delete

# Clean iOS build artifacts
echo "Cleaning iOS build artifacts..."
rm -rf ios/build
rm -rf ios/Pods
rm -rf ios/Podfile.lock

# Clean any remaining build directories
find . -name "build" -type d -exec rm -rf {} +
find . -path "*/intermediates/*" -exec rm -rf {} +
find . -path "*/outputs/*" -exec rm -rf {} +

echo "Cleanup complete!"
echo "You can now zip the repository with a much smaller size." 