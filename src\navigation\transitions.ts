import { Animated, Easing } from 'react-native';

/**
 * Fade transition configuration for screen transitions
 */

// Transition timing configuration for fade in/out
export const fadeTransitionSpec = {
  animation: 'timing' as const,
  config: {
    duration: 300,
    easing: Easing.inOut(Easing.ease),
    useNativeDriver: true,
  },
};

// Card style interpolator for fade transition
export const fadeCardStyleInterpolator = ({ current }: { current: { progress: Animated.AnimatedInterpolation<number> } }) => {
  return {
    cardStyle: {
      opacity: current.progress,
    },
    overlayStyle: {
      opacity: current.progress.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 0.5],
        extrapolate: 'clamp',
      }),
    },
  };
};

// Screen options for fade transition
export const fadeScreenOptions = {
  transitionSpec: {
    open: fadeTransitionSpec,
    close: fadeTransitionSpec,
  },
  cardStyleInterpolator: fadeCardStyleInterpolator,
};
