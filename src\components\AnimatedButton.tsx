import React, { useEffect } from 'react';
import {
  StyleProp,
  ViewStyle,
  TextStyle,
  ImageSourcePropType,
} from 'react-native';
import CustomButton from './CustomButton';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing
} from 'react-native-reanimated';

interface AnimatedButtonProps {
  text: string;
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  highlighted?: boolean;
  icon?: string;
  buttonContentContainerStyle?: StyleProp<ViewStyle>;
  image?: ImageSourcePropType;

}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  text,
  onPress,
  style,
  textStyle,
  highlighted = false,
  icon,
  buttonContentContainerStyle,
  image
}) => {
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.95);

  // Fade in animation when component mounts
  useEffect(() => {
    opacity.value = withTiming(1, { duration: 400, easing: Easing.out(Easing.ease) });
    scale.value = withTiming(1, { duration: 400, easing: Easing.out(Easing.ease) });
  }, [opacity, scale]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      transform: [{ scale: scale.value }],
    };
  });

  return (
    <Animated.View style={[animatedStyle]}>
      <CustomButton
        text={text}
        onPress={onPress}
        style={style}
        textStyle={textStyle}
        highlighted={highlighted}
        icon={icon}
        buttonContentContainerStyle={buttonContentContainerStyle}
        image={image}
      />
    </Animated.View>
  );
};

export default AnimatedButton;
