import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
  ScrollView,
  FlatList,
} from 'react-native';
import {moderateScale, scale} from '../utils/scale';
import TennisRacketSDK, {
  ProcessStatus,
  ProcessStatusEvent,
  RacketInfoEvent,
  RacketInfo,
  CabinetInfo,
  CabinetStatus,
  BallInfo,
  LockerInfo,
} from '../utils/TennisRacketSDK';

const TennisRacketSDKExample: React.FC = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasListener, setHasListener] = useState(false);
  const [status, setStatus] = useState('Not initialized');
  const [events, setEvents] = useState<string[]>([]);
  const [cabinetId, setCabinetId] = useState('1');
  const [racketId, setRacketId] = useState('R001');

  // New state variables for additional functionality
  const [rackets, setRackets] = useState<RacketInfo[]>([]);
  const [cabinets, setCabinets] = useState<CabinetInfo[]>([]);
  const [cabinetStatus, setCabinetStatus] = useState<CabinetStatus | null>(
    null,
  );
  const [showRackets, setShowRackets] = useState(false);
  const [showCabinets, setShowCabinets] = useState(false);

  // Ball vending state variables
  const [balls, setBalls] = useState<BallInfo[]>([]);
  const [showBalls, setShowBalls] = useState(false);
  const [selectedBallType, setSelectedBallType] = useState('Standard');
  const [ballQuantity, setBallQuantity] = useState('10');

  // Locker state variables
  const [lockers, setLockers] = useState<LockerInfo[]>([]);
  const [showLockers, setShowLockers] = useState(false);
  const [lockerId, setLockerId] = useState('L001');
  const [userId, setUserId] = useState('U001');

  // Initialize the SDK when the component mounts
  useEffect(() => {
    const initializeSDK = async () => {
      try {
        // Check if the SDK methods exist
        if (typeof TennisRacketSDK.initializeLogs !== 'function') {
          throw new Error(
            'SDK initialization methods not available. Make sure the native module is properly linked.',
          );
        }

        // Step 1: Initialize logs
        setStatus('Initializing logs...');
        try {
          await TennisRacketSDK.initializeLogs();
        } catch (logError) {
          console.warn(
            'Log initialization failed, continuing anyway:',
            logError,
          );
          // Continue even if log initialization fails
        }

        // Step 2: Initialize serial port
        setStatus('Initializing serial port...');
        let serialResult = false;
        try {
          serialResult = await TennisRacketSDK.initializeSerialPort();
        } catch (serialError) {
          console.error('Serial port initialization error:', serialError);
          setStatus(`Serial port error: ${serialError.message}`);
          // Try to continue anyway
        }

        // Step 3: Create result listener
        setStatus('Creating result listener...');
        let listenerResult = false;
        try {
          listenerResult = await TennisRacketSDK.createResultListener();
        } catch (listenerError) {
          console.error('Result listener error:', listenerError);
          setStatus(`Listener error: ${listenerError.message}`);
          // Try to continue anyway
        }

        // Set initialization status
        const initialized = serialResult || listenerResult;
        setIsInitialized(initialized);
        setHasListener(listenerResult);

        if (initialized) {
          setStatus('SDK initialized (some components may be in demo mode)');
        } else {
          setStatus('SDK initialization incomplete - running in demo mode');
        }
      } catch (error) {
        console.error('Error initializing SDK:', error);
        setStatus(`Initialization error: ${error.message}`);
        Alert.alert('Initialization Error', error.message);
      }
    };

    initializeSDK();

    // Set up event listeners
    const processStatusSubscription = TennisRacketSDK.addProcessStatusListener(
      (event: ProcessStatusEvent) => {
        console.log('Process status event received:', event);
        const eventText = `Status: ${event.status} - ${event.message}`;
        setEvents(prev => [eventText, ...prev]);

        if (event.status === ProcessStatus.SUCCESS) {
          setStatus(`Operation successful: ${event.message}`);
        } else if (event.status === ProcessStatus.FAILED) {
          setStatus(`Operation failed: ${event.message}`);
        } else {
          setStatus(`Operation status: ${event.status} - ${event.message}`);
        }
      },
    );

    const racketInfoSubscription = TennisRacketSDK.addRacketInfoListener(
      (event: RacketInfoEvent) => {
        console.log('Racket info event received:', event);
        const eventText = `Racket Info: ID=${event.id}, Name=${event.name}`;
        setEvents(prev => [eventText, ...prev]);
      },
    );

    // Clean up when component unmounts
    return () => {
      processStatusSubscription.remove();
      racketInfoSubscription.remove();
      TennisRacketSDK.removeAllListeners();
    };
  }, []);

  // Open cabinet
  const handleOpenCabinet = async () => {
    if (!isInitialized) {
      const demoEvent = {
        type: 'processStatus',
        status: ProcessStatus.SUCCESS,
        message: `Demo mode: Cabinet ${cabinetId} opened successfully`,
      };

      setEvents(prev => [
        `Status: ${demoEvent.status} - ${demoEvent.message}`,
        ...prev,
      ]);
      setStatus(`Demo mode: Cabinet ${cabinetId} opened`);
      return;
    }

    try {
      setStatus(`Opening cabinet: ${cabinetId}...`);

      if (typeof TennisRacketSDK.openCabinet !== 'function') {
        throw new Error('openCabinet method not available');
      }

      await TennisRacketSDK.openCabinet(cabinetId);
      setEvents(prev => [`Command sent: Open cabinet ${cabinetId}`, ...prev]);
    } catch (error) {
      console.error(`Error opening cabinet ${cabinetId}:`, error);
      setStatus(`Open cabinet error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to open cabinet - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Borrow racket
  const handleBorrowRacket = async () => {
    if (!isInitialized) {
      const demoEvent = {
        type: 'processStatus',
        status: ProcessStatus.SUCCESS,
        message: `Demo mode: Racket borrowed from cabinet ${cabinetId} successfully`,
      };

      setEvents(prev => [
        `Status: ${demoEvent.status} - ${demoEvent.message}`,
        ...prev,
      ]);
      setStatus(`Demo mode: Racket borrowed from cabinet ${cabinetId}`);
      return;
    }

    try {
      setStatus(`Borrowing racket from cabinet: ${cabinetId}...`);

      if (typeof TennisRacketSDK.borrowRacket !== 'function') {
        throw new Error('borrowRacket method not available');
      }

      await TennisRacketSDK.borrowRacket(cabinetId);
      setEvents(prev => [
        `Command sent: Borrow racket from cabinet ${cabinetId}`,
        ...prev,
      ]);
    } catch (error) {
      console.error(`Error borrowing racket from cabinet ${cabinetId}:`, error);
      setStatus(`Borrow racket error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to borrow racket - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Return racket
  const handleReturnRacket = async () => {
    if (!isInitialized) {
      const demoEvent = {
        type: 'processStatus',
        status: ProcessStatus.SUCCESS,
        message: `Demo mode: Racket ${racketId} returned to cabinet ${cabinetId} successfully`,
      };

      setEvents(prev => [
        `Status: ${demoEvent.status} - ${demoEvent.message}`,
        ...prev,
      ]);
      setStatus(
        `Demo mode: Racket ${racketId} returned to cabinet ${cabinetId}`,
      );
      return;
    }

    try {
      setStatus(`Returning racket ${racketId} to cabinet: ${cabinetId}...`);

      if (typeof TennisRacketSDK.returnRacket !== 'function') {
        throw new Error('returnRacket method not available');
      }

      await TennisRacketSDK.returnRacket(cabinetId, racketId);
      setEvents(prev => [
        `Command sent: Return racket ${racketId} to cabinet ${cabinetId}`,
        ...prev,
      ]);
    } catch (error) {
      console.error(
        `Error returning racket ${racketId} to cabinet ${cabinetId}:`,
        error,
      );
      setStatus(`Return racket error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to return racket - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Get racket info
  const handleGetRacketInfo = async () => {
    if (!isInitialized) {
      // Demo mode - return fake racket info
      const demoRacketInfo = {
        id: racketId,
        name: 'Demo Racket',
        status: 'Available',
        brand: 'Demo Brand',
        weight: 'Medium',
      };

      const infoText = `Demo Mode - Racket Info: ID=${demoRacketInfo.id}, Name=${demoRacketInfo.name}, Status=${demoRacketInfo.status}`;
      setEvents(prev => [infoText, ...prev]);
      setStatus(`Demo mode: Got info for racket ${racketId}`);
      return;
    }

    try {
      setStatus(`Getting info for racket: ${racketId}...`);

      if (typeof TennisRacketSDK.getRacketInfo !== 'function') {
        throw new Error('getRacketInfo method not available');
      }

      const racketInfo = await TennisRacketSDK.getRacketInfo(racketId);

      if (racketInfo) {
        const infoText = `Racket Info: ID=${racketInfo.id}, Name=${racketInfo.name}, Status=${racketInfo.status}, Brand=${racketInfo.brand}, Weight=${racketInfo.weight}`;
        setEvents(prev => [infoText, ...prev]);
        setStatus(`Got racket info for: ${racketId}`);
      } else {
        const notFoundText = `No info found for racket: ${racketId}`;
        setEvents(prev => [notFoundText, ...prev]);
        setStatus(notFoundText);
      }
    } catch (error) {
      console.error(`Error getting racket info for ${racketId}:`, error);
      setStatus(`Get racket info error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to get racket info - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Get all rackets
  const handleGetAllRackets = async () => {
    if (!isInitialized) {
      // Demo mode - return fake racket list
      const demoRackets = [
        {
          id: 'R001',
          name: 'Demo Racket 1',
          status: 'Available',
          brand: 'Head',
          weight: 'Light',
        },
        {
          id: 'R002',
          name: 'Demo Racket 2',
          status: 'In Use',
          brand: 'Wilson',
          weight: 'Medium',
        },
        {
          id: 'R003',
          name: 'Demo Racket 3',
          status: 'Available',
          brand: 'Babolat',
          weight: 'Heavy',
        },
      ];

      setRackets(demoRackets);
      setShowRackets(true);
      setShowCabinets(false);
      setStatus(`Demo mode: Got ${demoRackets.length} rackets`);
      return;
    }

    try {
      setStatus('Getting all rackets...');

      if (typeof TennisRacketSDK.getAllRackets !== 'function') {
        throw new Error('getAllRackets method not available');
      }

      const allRackets = await TennisRacketSDK.getAllRackets();

      setRackets(allRackets);
      setShowRackets(true);
      setShowCabinets(false);
      setStatus(`Got ${allRackets.length} rackets`);
      setEvents(prev => [`Got ${allRackets.length} rackets`, ...prev]);
    } catch (error) {
      console.error('Error getting all rackets:', error);
      setStatus(`Get all rackets error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to get all rackets - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Get all cabinets
  const handleGetAllCabinets = async () => {
    if (!isInitialized) {
      // Demo mode - return fake cabinet list
      const demoCabinets = [
        {id: '1', name: 'Cabinet 1', isOccupied: true, racketId: 'R001'},
        {id: '2', name: 'Cabinet 2', isOccupied: false, racketId: ''},
        {id: '3', name: 'Cabinet 3', isOccupied: true, racketId: 'R003'},
      ];

      setCabinets(demoCabinets);
      setShowCabinets(true);
      setShowRackets(false);
      setStatus(`Demo mode: Got ${demoCabinets.length} cabinets`);
      return;
    }

    try {
      setStatus('Getting all cabinets...');

      if (typeof TennisRacketSDK.getAllCabinets !== 'function') {
        throw new Error('getAllCabinets method not available');
      }

      const allCabinets = await TennisRacketSDK.getAllCabinets();

      setCabinets(allCabinets);
      setShowCabinets(true);
      setShowRackets(false);
      setStatus(`Got ${allCabinets.length} cabinets`);
      setEvents(prev => [`Got ${allCabinets.length} cabinets`, ...prev]);
    } catch (error) {
      console.error('Error getting all cabinets:', error);
      setStatus(`Get all cabinets error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to get all cabinets - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Get cabinet status
  const handleGetCabinetStatus = async () => {
    if (!isInitialized) {
      // Demo mode - return fake cabinet status
      const demoCabinetStatus = {
        id: cabinetId,
        isOccupied: true,
        racketId: 'R001',
      };

      setCabinetStatus(demoCabinetStatus);
      const statusText = `Demo Mode - Cabinet Status: ID=${demoCabinetStatus.id}, Occupied=${demoCabinetStatus.isOccupied}, Racket=${demoCabinetStatus.racketId}`;
      setEvents(prev => [statusText, ...prev]);
      setStatus(`Demo mode: Got status for cabinet ${cabinetId}`);
      return;
    }

    try {
      setStatus(`Getting status for cabinet: ${cabinetId}...`);

      if (typeof TennisRacketSDK.getCabinetStatus !== 'function') {
        throw new Error('getCabinetStatus method not available');
      }

      const status = await TennisRacketSDK.getCabinetStatus(cabinetId);

      setCabinetStatus(status);
      const statusText = `Cabinet Status: ID=${status.id}, Occupied=${status.isOccupied}, Racket=${status.racketId}`;
      setEvents(prev => [statusText, ...prev]);
      setStatus(`Got status for cabinet: ${cabinetId}`);
    } catch (error) {
      console.error(`Error getting cabinet status for ${cabinetId}:`, error);
      setStatus(`Get cabinet status error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to get cabinet status - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Reset machine
  const handleResetMachine = async () => {
    if (!isInitialized) {
      // Demo mode
      setStatus('Demo mode: Machine reset command sent');
      setEvents(prev => ['Demo Mode - Machine reset command sent', ...prev]);
      return;
    }

    try {
      setStatus('Resetting machine...');

      if (typeof TennisRacketSDK.resetMachine !== 'function') {
        throw new Error('resetMachine method not available');
      }

      await TennisRacketSDK.resetMachine();

      setStatus('Machine reset command sent');
      setEvents(prev => ['Machine reset command sent', ...prev]);
    } catch (error) {
      console.error('Error resetting machine:', error);
      setStatus(`Reset machine error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to reset machine - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Get available balls
  const handleGetAvailableBalls = async () => {
    if (!isInitialized) {
      // Demo mode - return fake ball info
      const demoBalls = [
        {type: 'Standard', quantity: 100, available: true},
        {type: 'Premium', quantity: 50, available: true},
        {type: 'Training', quantity: 75, available: true},
      ];

      setBalls(demoBalls);
      setShowBalls(true);
      setShowRackets(false);
      setShowCabinets(false);
      setShowLockers(false);
      setStatus(`Demo mode: Got ${demoBalls.length} ball types`);
      return;
    }

    try {
      setStatus('Getting available balls...');

      if (typeof TennisRacketSDK.getAvailableBalls !== 'function') {
        throw new Error('getAvailableBalls method not available');
      }

      const availableBalls = await TennisRacketSDK.getAvailableBalls();

      setBalls(availableBalls);
      setShowBalls(true);
      setShowRackets(false);
      setShowCabinets(false);
      setShowLockers(false);
      setStatus(`Got ${availableBalls.length} ball types`);
      setEvents(prev => [`Got ${availableBalls.length} ball types`, ...prev]);
    } catch (error) {
      console.error('Error getting available balls:', error);
      setStatus(`Get available balls error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to get available balls - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Dispense balls
  const handleDispenseBalls = async () => {
    if (!isInitialized) {
      // Demo mode
      setStatus(
        `Demo mode: Dispensing ${ballQuantity} ${selectedBallType} balls`,
      );
      setEvents(prev => [
        `Demo Mode - Dispensing ${ballQuantity} ${selectedBallType} balls`,
        ...prev,
      ]);
      return;
    }

    try {
      setStatus(`Dispensing ${ballQuantity} ${selectedBallType} balls...`);

      if (typeof TennisRacketSDK.dispenseBalls !== 'function') {
        throw new Error('dispenseBalls method not available');
      }

      await TennisRacketSDK.dispenseBalls(
        selectedBallType,
        parseInt(ballQuantity, 10),
      );

      setStatus(`Dispensing ${ballQuantity} ${selectedBallType} balls`);
      setEvents(prev => [
        `Dispensing ${ballQuantity} ${selectedBallType} balls`,
        ...prev,
      ]);
    } catch (error) {
      console.error('Error dispensing balls:', error);
      setStatus(`Dispense balls error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to dispense balls - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Get all lockers
  const handleGetAllLockers = async () => {
    if (!isInitialized) {
      // Demo mode - return fake locker list
      const demoLockers = [
        {id: 'L001', name: 'Locker 1', isOccupied: false, userId: ''},
        {id: 'L002', name: 'Locker 2', isOccupied: true, userId: 'U001'},
        {id: 'L003', name: 'Locker 3', isOccupied: false, userId: ''},
      ];

      setLockers(demoLockers);
      setShowLockers(true);
      setShowRackets(false);
      setShowCabinets(false);
      setShowBalls(false);
      setStatus(`Demo mode: Got ${demoLockers.length} lockers`);
      return;
    }

    try {
      setStatus('Getting all lockers...');

      if (typeof TennisRacketSDK.getAllLockers !== 'function') {
        throw new Error('getAllLockers method not available');
      }

      const allLockers = await TennisRacketSDK.getAllLockers();

      setLockers(allLockers);
      setShowLockers(true);
      setShowRackets(false);
      setShowCabinets(false);
      setShowBalls(false);
      setStatus(`Got ${allLockers.length} lockers`);
      setEvents(prev => [`Got ${allLockers.length} lockers`, ...prev]);
    } catch (error) {
      console.error('Error getting all lockers:', error);
      setStatus(`Get all lockers error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to get all lockers - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Open locker
  const handleOpenLocker = async () => {
    if (!isInitialized) {
      // Demo mode
      setStatus(`Demo mode: Opening locker ${lockerId}`);
      setEvents(prev => [`Demo Mode - Opening locker ${lockerId}`, ...prev]);
      return;
    }

    try {
      setStatus(`Opening locker ${lockerId}...`);

      if (typeof TennisRacketSDK.openLocker !== 'function') {
        throw new Error('openLocker method not available');
      }

      await TennisRacketSDK.openLocker(lockerId);

      setStatus(`Opening locker ${lockerId}`);
      setEvents(prev => [`Opening locker ${lockerId}`, ...prev]);
    } catch (error) {
      console.error('Error opening locker:', error);
      setStatus(`Open locker error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to open locker - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Assign locker
  const handleAssignLocker = async () => {
    if (!isInitialized) {
      // Demo mode
      setStatus(`Demo mode: Assigning locker ${lockerId} to user ${userId}`);
      setEvents(prev => [
        `Demo Mode - Assigning locker ${lockerId} to user ${userId}`,
        ...prev,
      ]);
      return;
    }

    try {
      setStatus(`Assigning locker ${lockerId} to user ${userId}...`);

      if (typeof TennisRacketSDK.assignLocker !== 'function') {
        throw new Error('assignLocker method not available');
      }

      await TennisRacketSDK.assignLocker(lockerId, userId);

      setStatus(`Assigned locker ${lockerId} to user ${userId}`);
      setEvents(prev => [
        `Assigned locker ${lockerId} to user ${userId}`,
        ...prev,
      ]);
    } catch (error) {
      console.error('Error assigning locker:', error);
      setStatus(`Assign locker error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to assign locker - ${error.message}`,
        ...prev,
      ]);
    }
  };

  // Release locker
  const handleReleaseLocker = async () => {
    if (!isInitialized) {
      // Demo mode
      setStatus(`Demo mode: Releasing locker ${lockerId}`);
      setEvents(prev => [`Demo Mode - Releasing locker ${lockerId}`, ...prev]);
      return;
    }

    try {
      setStatus(`Releasing locker ${lockerId}...`);

      if (typeof TennisRacketSDK.releaseLocker !== 'function') {
        throw new Error('releaseLocker method not available');
      }

      await TennisRacketSDK.releaseLocker(lockerId);

      setStatus(`Released locker ${lockerId}`);
      setEvents(prev => [`Released locker ${lockerId}`, ...prev]);
    } catch (error) {
      console.error('Error releasing locker:', error);
      setStatus(`Release locker error: ${error.message}`);

      // Add to event log
      setEvents(prev => [
        `Error: Failed to release locker - ${error.message}`,
        ...prev,
      ]);
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Tennis Racket SDK</Text>
        <View style={styles.statusBadge}>
          <Text style={styles.statusLabel}>Status:</Text>
          <Text style={styles.statusText}>{status}</Text>
        </View>
      </View>

      <View style={styles.contentContainer}>
        {/* Left Panel - Controls */}
        <ScrollView style={styles.leftPanel}>
          {/* Racket Operations Section */}
          <View style={styles.sectionCard}>
            <Text style={styles.sectionHeader}>Racket Operations</Text>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Cabinet ID:</Text>
              <TextInput
                style={styles.input}
                value={cabinetId}
                onChangeText={setCabinetId}
                keyboardType="numeric"
                placeholder="Enter cabinet ID"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Racket ID:</Text>
              <TextInput
                style={styles.input}
                value={racketId}
                onChangeText={setRacketId}
                placeholder="Enter racket ID"
              />
            </View>

            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  (!isInitialized || !hasListener) && styles.disabledButton,
                ]}
                onPress={handleOpenCabinet}
                disabled={!isInitialized || !hasListener}>
                <Text style={styles.buttonText}>Open Cabinet</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  (!isInitialized || !hasListener) && styles.disabledButton,
                ]}
                onPress={handleBorrowRacket}
                disabled={!isInitialized || !hasListener}>
                <Text style={styles.buttonText}>Borrow Racket</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  (!isInitialized || !hasListener) && styles.disabledButton,
                ]}
                onPress={handleReturnRacket}
                disabled={!isInitialized || !hasListener}>
                <Text style={styles.buttonText}>Return Racket</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[
                  styles.infoButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleGetRacketInfo}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Get Racket Info</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.infoButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleGetAllRackets}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Get All Rackets</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.infoButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleGetAllCabinets}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Get All Cabinets</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.infoButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleGetCabinetStatus}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Get Cabinet Status</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Ball Vending Section */}
          <View style={styles.sectionCard}>
            <Text style={styles.sectionHeader}>Ball Vending</Text>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Ball Type:</Text>
              <TextInput
                style={styles.input}
                value={selectedBallType}
                onChangeText={setSelectedBallType}
                placeholder="Enter ball type"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Quantity:</Text>
              <TextInput
                style={styles.input}
                value={ballQuantity}
                onChangeText={setBallQuantity}
                keyboardType="numeric"
                placeholder="Enter quantity"
              />
            </View>

            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[
                  styles.infoButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleGetAvailableBalls}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Get Available Balls</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  (!isInitialized || !hasListener) && styles.disabledButton,
                ]}
                onPress={handleDispenseBalls}
                disabled={!isInitialized || !hasListener}>
                <Text style={styles.buttonText}>Dispense Balls</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Locker Operations Section */}
          <View style={styles.sectionCard}>
            <Text style={styles.sectionHeader}>Locker Operations</Text>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Locker ID:</Text>
              <TextInput
                style={styles.input}
                value={lockerId}
                onChangeText={setLockerId}
                placeholder="Enter locker ID"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>User ID:</Text>
              <TextInput
                style={styles.input}
                value={userId}
                onChangeText={setUserId}
                placeholder="Enter user ID"
              />
            </View>

            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[
                  styles.infoButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleGetAllLockers}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Get All Lockers</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  (!isInitialized || !hasListener) && styles.disabledButton,
                ]}
                onPress={handleOpenLocker}
                disabled={!isInitialized || !hasListener}>
                <Text style={styles.buttonText}>Open Locker</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleAssignLocker}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Assign Locker</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.actionButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleReleaseLocker}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Release Locker</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Maintenance Operations Section */}
          <View style={styles.sectionCard}>
            <Text style={styles.sectionHeader}>Maintenance Operations</Text>
            <View style={styles.buttonGroup}>
              <TouchableOpacity
                style={[
                  styles.resetButton,
                  !isInitialized && styles.disabledButton,
                ]}
                onPress={handleResetMachine}
                disabled={!isInitialized}>
                <Text style={styles.buttonText}>Reset Machine</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        {/* Right Panel - Results and Logs */}
        <View style={styles.rightPanel}>
          <ScrollView style={styles.resultsContainer}>
            {/* Results Section */}
            {showRackets && (
              <View style={styles.resultCard}>
                <Text style={styles.resultTitle}>Available Rackets:</Text>
                {rackets.length === 0 ? (
                  <Text style={styles.noResultsText}>No rackets found</Text>
                ) : (
                  <FlatList
                    data={rackets}
                    keyExtractor={item => item.id}
                    renderItem={({item}) => (
                      <View style={styles.resultItem}>
                        <Text style={styles.resultItemTitle}>
                          {item.name} ({item.id})
                        </Text>
                        <Text style={styles.resultItemDetail}>
                          Status: {item.status}
                        </Text>
                        <Text style={styles.resultItemDetail}>
                          Brand: {item.brand}
                        </Text>
                        <Text style={styles.resultItemDetail}>
                          Weight: {item.weight}
                        </Text>
                      </View>
                    )}
                  />
                )}
              </View>
            )}

            {showCabinets && (
              <View style={styles.resultCard}>
                <Text style={styles.resultTitle}>Cabinets:</Text>
                {cabinets.length === 0 ? (
                  <Text style={styles.noResultsText}>No cabinets found</Text>
                ) : (
                  <FlatList
                    data={cabinets}
                    keyExtractor={item => item.id}
                    renderItem={({item}) => (
                      <View style={styles.resultItem}>
                        <Text style={styles.resultItemTitle}>
                          {item.name} ({item.id})
                        </Text>
                        <Text style={styles.resultItemDetail}>
                          Status: {item.isOccupied ? 'Occupied' : 'Empty'}
                        </Text>
                        {item.isOccupied && item.racketId && (
                          <Text style={styles.resultItemDetail}>
                            Racket ID: {item.racketId}
                          </Text>
                        )}
                      </View>
                    )}
                  />
                )}
              </View>
            )}

            {cabinetStatus && (
              <View style={styles.resultCard}>
                <Text style={styles.resultTitle}>Cabinet Status:</Text>
                <View style={styles.resultItem}>
                  <Text style={styles.resultItemTitle}>
                    Cabinet {cabinetStatus.id}
                  </Text>
                  <Text style={styles.resultItemDetail}>
                    Status: {cabinetStatus.isOccupied ? 'Occupied' : 'Empty'}
                  </Text>
                  {cabinetStatus.isOccupied && cabinetStatus.racketId && (
                    <Text style={styles.resultItemDetail}>
                      Racket ID: {cabinetStatus.racketId}
                    </Text>
                  )}
                </View>
              </View>
            )}

            {showBalls && (
              <View style={styles.resultCard}>
                <Text style={styles.resultTitle}>Available Balls:</Text>
                {balls.length === 0 ? (
                  <Text style={styles.noResultsText}>No balls found</Text>
                ) : (
                  <FlatList
                    data={balls}
                    keyExtractor={item => item.type}
                    renderItem={({item}) => (
                      <View style={styles.resultItem}>
                        <Text style={styles.resultItemTitle}>{item.type}</Text>
                        <Text style={styles.resultItemDetail}>
                          Quantity: {item.quantity}
                        </Text>
                        <Text style={styles.resultItemDetail}>
                          Status:{' '}
                          {item.available ? 'Available' : 'Not Available'}
                        </Text>
                      </View>
                    )}
                  />
                )}
              </View>
            )}

            {showLockers && (
              <View style={styles.resultCard}>
                <Text style={styles.resultTitle}>Lockers:</Text>
                {lockers.length === 0 ? (
                  <Text style={styles.noResultsText}>No lockers found</Text>
                ) : (
                  <FlatList
                    data={lockers}
                    keyExtractor={item => item.id}
                    renderItem={({item}) => (
                      <View style={styles.resultItem}>
                        <Text style={styles.resultItemTitle}>
                          {item.name} ({item.id})
                        </Text>
                        <Text style={styles.resultItemDetail}>
                          Status: {item.isOccupied ? 'Occupied' : 'Empty'}
                        </Text>
                        {item.isOccupied && item.userId && (
                          <Text style={styles.resultItemDetail}>
                            User ID: {item.userId}
                          </Text>
                        )}
                      </View>
                    )}
                  />
                )}
              </View>
            )}
          </ScrollView>

          {/* Log Section - Always visible on the right */}
          <View style={styles.logContainer}>
            <Text style={styles.logTitle}>Event Log:</Text>
            <ScrollView style={styles.logScrollView}>
              {events.length === 0 ? (
                <Text style={styles.noLogText}>No events yet</Text>
              ) : (
                events.map((event, index) => (
                  <Text key={index} style={styles.logText}>
                    {event}
                  </Text>
                ))
              )}
            </ScrollView>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Main container
  mainContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    // marginTop: scale(100),
    width: '70%',
    // height: '100%',
    margin: 'auto',
  },

  // Header
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: scale(15),
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: moderateScale(24),
    fontWeight: 'bold',
    color: '#333',
  },
  statusBadge: {
    backgroundColor: '#f0f0f0',
    padding: scale(8),
    borderRadius: scale(8),
    maxWidth: '50%',
  },
  statusLabel: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(4),
    color: '#555',
  },
  statusText: {
    fontSize: moderateScale(20),
    color: '#333',
  },

  // Content layout
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    padding: scale(10),
  },
  leftPanel: {
    flex: 1,
    padding: scale(10),
    marginRight: scale(10),
  },
  rightPanel: {
    flex: 1,
    padding: scale(10),
    marginLeft: scale(10),
    flexDirection: 'column',
  },

  // Section cards
  sectionCard: {
    backgroundColor: '#fff',
    borderRadius: scale(8),
    padding: scale(15),
    marginBottom: scale(15),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    marginBottom: scale(15),
    color: '#333',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: scale(8),
  },

  // Input groups
  inputGroup: {
    marginBottom: scale(15),
  },
  inputLabel: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(5),
    color: '#555',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: scale(8),
    padding: scale(12),
    fontSize: moderateScale(20),
    backgroundColor: '#fafafa',
  },

  // Button groups
  buttonGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: scale(10),
    marginBottom: scale(5),
  },
  actionButton: {
    backgroundColor: '#2196F3',
    padding: scale(12),
    borderRadius: scale(8),
    alignItems: 'center',
    marginBottom: scale(10),
    flex: 1,
    marginHorizontal: scale(5),
    minWidth: '45%',
  },
  infoButton: {
    backgroundColor: '#4CAF50',
    padding: scale(12),
    borderRadius: scale(8),
    alignItems: 'center',
    marginBottom: scale(10),
    flex: 1,
    marginHorizontal: scale(5),
    minWidth: '45%',
  },
  resetButton: {
    backgroundColor: '#F44336',
    padding: scale(12),
    borderRadius: scale(8),
    alignItems: 'center',
    marginBottom: scale(10),
    width: '100%',
  },
  disabledButton: {
    backgroundColor: '#B0BEC5',
  },
  buttonText: {
    color: '#fff',
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    textAlign: 'center',
  },

  // Results section
  resultsContainer: {
    flex: 2,
    backgroundColor: '#fff',
    borderRadius: scale(8),
    padding: scale(15),
    marginBottom: scale(15),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    maxHeight: '60%',
  },
  resultCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: scale(8),
    padding: scale(12),
    marginBottom: scale(15),
    borderWidth: 1,
    borderColor: '#eee',
  },
  resultTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(10),
    color: '#333',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: scale(5),
  },
  resultItem: {
    padding: scale(10),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: scale(5),
  },
  resultItemTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(5),
    color: '#333',
  },
  resultItemDetail: {
    fontSize: moderateScale(20),
    color: '#666',
    marginBottom: scale(3),
  },
  noResultsText: {
    fontStyle: 'italic',
    color: '#757575',
    textAlign: 'center',
    padding: scale(10),
  },

  // Log section
  logContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: scale(8),
    padding: scale(15),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    maxHeight: '40%',
  },
  logTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(10),
    color: '#333',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: scale(5),
  },
  logScrollView: {
    flex: 1,
  },
  logText: {
    fontSize: moderateScale(20),
    marginBottom: scale(5),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: scale(5),
    color: '#555',
  },
  noLogText: {
    fontStyle: 'italic',
    color: '#757575',
    textAlign: 'center',
    padding: scale(10),
  },

  // Legacy styles for backward compatibility
  container: {
    flex: 1,
    padding: scale(20),
    backgroundColor: '#f5f5f5',
  },
  sectionTitle: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    marginTop: scale(10),
    marginBottom: scale(10),
    color: '#333',
  },
  statusContainer: {
    backgroundColor: '#fff',
    padding: scale(15),
    borderRadius: scale(8),
    marginBottom: scale(20),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    backgroundColor: '#fff',
    padding: scale(15),
    borderRadius: scale(8),
    marginBottom: scale(20),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  buttonContainer: {
    gap: scale(10),
    marginBottom: scale(20),
  },
  button: {
    backgroundColor: '#2196F3',
    padding: scale(15),
    borderRadius: scale(8),
    alignItems: 'center',
  },
  eventsContainer: {
    backgroundColor: '#fff',
    padding: scale(15),
    borderRadius: scale(8),
    marginBottom: scale(20),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    maxHeight: scale(200),
  },
  eventsTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(10),
  },
  noEventsText: {
    fontStyle: 'italic',
    color: '#757575',
  },
  eventText: {
    fontSize: moderateScale(20),
    marginBottom: scale(5),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: scale(5),
  },
  listContainer: {
    backgroundColor: '#fff',
    padding: scale(15),
    borderRadius: scale(8),
    marginBottom: scale(20),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    maxHeight: scale(300),
  },
  listTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(10),
  },
  listItem: {
    padding: scale(10),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: scale(5),
  },
  listItemTitle: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    marginBottom: scale(5),
  },
  listItemDetail: {
    fontSize: moderateScale(20),
    color: '#666',
    marginBottom: scale(3),
  },
});

export default TennisRacketSDKExample;
