import * as Sentry from '@sentry/react-native';

/**
 * Log an error to Sen<PERSON> with additional context
 * @param error Error to log
 * @param context Additional context
 */
export const logError = (
  error: Error,
  context: Record<string, unknown> = {},
) => {
  Sentry.withScope(scope => {
    // Add context to the scope
    for (const [key, value] of Object.entries(context)) {
      scope.setExtra(key, value);
    }

    // Capture the error
    Sentry.captureException(error);
  });
};
