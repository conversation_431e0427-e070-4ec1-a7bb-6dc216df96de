import React, {createContext, useContext, useState, ReactNode} from 'react';

type FlowType = 'CHECKOUT' | 'RETURN' | null;

interface ReservationContextType {
  flowType: FlowType;
  setFlowType: (type: FlowType) => void;
}

const ReservationContext = createContext<ReservationContextType | undefined>(
  undefined,
);

export const ReservationProvider: React.FC<{children: ReactNode}> = ({
  children,
}) => {
  const [flowType, setFlowType] = useState<FlowType>(null);

  return (
    <ReservationContext.Provider value={{flowType, setFlowType}}>
      {children}
    </ReservationContext.Provider>
  );
};

export const useReservation = () => {
  const context = useContext(ReservationContext);
  if (context === undefined) {
    throw new Error('useReservation must be used within a ReservationProvider');
  }
  return context;
};
