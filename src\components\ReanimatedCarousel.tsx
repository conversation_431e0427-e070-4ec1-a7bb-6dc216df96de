import React, {useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ViewStyle,
  StyleProp,
  Image,
  ImageSourcePropType,
} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import Icon from './Icon';
import KioskConfig from '@/config/KioskConfig';
import Typography from './Typography';
import FastImage, {Source} from 'react-native-fast-image';
import {moderateScale, scale, verticalScale} from '@/utils/scale';
const {width: screenWidth} = Dimensions.get('window');

interface CarouselItem {
  id: string;
  component?: React.ReactNode;
  image?: Source;
  title?: string;
  description?: string;
}

interface ReanimatedCarouselProps {
  data: CarouselItem[];
  width?: number;
  height?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showArrows?: boolean;
  showPagination?: boolean;
  loop?: boolean;
  style?: StyleProp<ViewStyle>;
  itemStyle?: StyleProp<ViewStyle>;
  onSnapToItem?: (index: number) => void;
  customRenderItem?: (item: CarouselItem, index: number) => React.ReactNode;
  initialIndex?: number;
}

const ReanimatedCarousel: React.FC<ReanimatedCarouselProps> = ({
  data,
  width = screenWidth * 0.8,
  height = 400,
  autoPlay = false,
  autoPlayInterval = 3000,
  showArrows = true,
  showPagination = true,
  loop = true,
  style,
  itemStyle,
  onSnapToItem,
  customRenderItem,
  initialIndex = 0,
}) => {
  const [activeIndex, setActiveIndex] = useState(initialIndex);
  const carouselRef = useRef(null);
  const progressValue = useSharedValue<number>(0);

  const handleSnapToItem = (index: number) => {
    setActiveIndex(index);
    if (onSnapToItem) {
      onSnapToItem(index);
    }
  };

  const handleNext = () => {
    if (carouselRef.current) {
      // @ts-ignore - The type definitions for the carousel are incomplete
      carouselRef.current.next();
    }
  };

  const handlePrev = () => {
    if (carouselRef.current) {
      // @ts-ignore - The type definitions for the carousel are incomplete
      carouselRef.current.prev();
    }
  };

  const defaultRenderItem = ({
    item,
    index,
  }: {
    item: CarouselItem;
    index: number;
  }) => {
    return (
      <View style={[styles.itemContainer, itemStyle]}>
        {item.image && (
          <FastImage
            source={item.image}
            style={styles.image}
            resizeMode="contain"
          />
        )}
        {item.title && (
          <Typography variant="faqAnswer" style={styles.title}>
            {item.title}
          </Typography>
        )}
        {item.description && (
          <Typography variant="customButton" style={styles.description}>
            {item.description}
          </Typography>
        )}
        {item.component}
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {showArrows && (
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.leftNavButton,
            !loop && activeIndex === 0 && styles.navButtonDisabled,
          ]}
          onPress={handlePrev}
          disabled={!loop && activeIndex === 0}>
          <Icon
            name="Left-chevron"
            size={scale(30)}
            color={KioskConfig.theme.colors.white}
          />
        </TouchableOpacity>
      )}

      <Carousel
        ref={carouselRef}
        loop={loop}
        width={width}
        height={height}
        autoPlay={autoPlay}
        autoPlayInterval={autoPlayInterval}
        data={data}
        scrollAnimationDuration={500}
        onProgressChange={(_, absoluteProgress) => {
          progressValue.value = absoluteProgress;
        }}
        onSnapToItem={handleSnapToItem}
        renderItem={({item, index}) =>
          customRenderItem
            ? customRenderItem(item, index)
            : defaultRenderItem({item, index})
        }
        panGestureHandlerProps={{
          activeOffsetX: [-10, 10],
        }}
        defaultIndex={initialIndex}
      />

      {showArrows && (
        <TouchableOpacity
          style={[
            styles.navButton,
            styles.rightNavButton,
            !loop &&
              activeIndex === data.length - 1 &&
              styles.navButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={!loop && activeIndex === data.length - 1}>
          <Icon
            name="Right-chevron"
            size={scale(30)}
            color={KioskConfig.theme.colors.white}
          />
        </TouchableOpacity>
      )}

      {showPagination && (
        <View style={styles.paginationContainer}>
          {data.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === activeIndex && styles.paginationDotActive,
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  itemContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: scale(10),
    padding: scale(20),
  },
  image: {
    width: '100%',
    height: '70%',
    marginBottom: scale(15),
  },
  title: {
    fontSize: scale(24),
    fontWeight: 'bold',
    marginBottom: scale(10),
    textAlign: 'center',
  },
  description: {
    fontSize: scale(16),
    textAlign: 'center',
  },
  navButton: {
    position: 'absolute',
    zIndex: 10,
    width: scale(50),
    height: verticalScale(50),
    borderRadius: scale(25),
  },
  leftNavButton: {
    left: scale(-25),
  },
  rightNavButton: {
    right: scale(-25),
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: scale(20),
  },
  paginationDot: {
    width: scale(10),
    height: verticalScale(10),
    borderRadius: scale(5),
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    margin: scale(5),
  },
  paginationDotActive: {
    backgroundColor: KioskConfig.theme.colors.blue || '#007AFF',
    width: scale(12),
    height: verticalScale(12),
    borderRadius: scale(6),
  },
});

export default ReanimatedCarousel;
