import React, {memo, useState} from 'react';
import {
  ImageRequireSource,
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import images from '@/config/images';
import Typography from '../Typography';
import KioskConfig from '@/config/KioskConfig';
import Icon from '../Icon';
import {scale, verticalScale} from '@/utils/scale';
import FastImage, {ImageStyle} from 'react-native-fast-image';
import {useLanguageStore} from '@/store/languageStore';
import LottieView from 'lottie-react-native';
import animation from '@/config/animation';

const BrandedRacquetCategory = ({
  image,
  type,
  style = {},
  title,
  addOnPress,
}: {
  image: ImageRequireSource;
  style?: StyleProp<ImageStyle>;
  title?: string;
  addOnPress?: () => void;
  type?: string;
}) => {
  const {t} = useLanguageStore();
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePress = () => {
    if (!isAnimating) {
      setIsAnimating(true);
    }
  };

  const handleAnimationFinish = () => {
    setIsAnimating(false);
    if (addOnPress) {
      addOnPress();
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      style={styles.root}
      onPress={handlePress}>
      <FastImage
        source={image || images.dunlopCx200}
        style={[styles.image, style as ImageStyle]}
        resizeMode="contain"
      />

      <View style={styles.addIcon}>
        {/* <Icon
          name="add"
          size={scale(78)}
          color={KioskConfig.theme.colors.Mindaro}
        /> */}
        <View style={styles.addIconContainer}>
          <LottieView
            source={animation.plusIconAnimation}
            autoPlay={isAnimating}
            loop={false}
            onAnimationFinish={handleAnimationFinish}
            style={styles.plusBtn}
          />
        </View>
        <Typography
          variant="tryText"
          align="center"
          color={KioskConfig.theme.colors.white}
          style={styles.tryText}>
          {t('brandedRacquetCategory.tryOrBuy')}
        </Typography>
      </View>
      <View style={styles.titleContainer}>
        <Typography
          variant="racquetType"
          color={KioskConfig.theme.colors.white}
          numberOfLines={2}
          style={styles.title}>
          {type}
        </Typography>
        <Typography
          variant="racquetTitle"
          color={KioskConfig.theme.colors.white}
          numberOfLines={2}
          style={styles.title}>
          {title}
        </Typography>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  image: {
    width: scale(187),
    height: verticalScale(612),
  },
  title: {
    textAlign: 'center',
  },
  addIcon: {
    position: 'absolute',
    top: verticalScale(40),
    right: scale(-120),
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center',
    maxWidth: scale(100),
  },
  titleContainer: {
    marginTop: verticalScale(10),
    gap: scale(10),
  },
  tryText: {
    marginTop: verticalScale(0),
  },
  addIconContainer: {
    width: scale(100),
    height: verticalScale(100),
    justifyContent: 'center',
    alignItems: 'center',
  },
  plusBtn: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default memo(BrandedRacquetCategory);
