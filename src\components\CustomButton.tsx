import React, {useState, useRef} from 'react';
import {
  TouchableHighlight,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
  Modal,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import Typography from './Typography';
import Icon from './Icon';
import KioskConfig from '@/config/KioskConfig';
import {scale, verticalScale} from '@/utils/scale';
import animation from '@/config/animation';
import LottieView, {AnimationObject} from 'lottie-react-native';
import {getBrandName} from '@/utils/CommonFunctions';

interface CustomButtonProps {
  text: string;
  text2?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  highlighted?: boolean;
  icon?: string;
  buttonContentContainerStyle?: StyleProp<ViewStyle>;
  size?: number;
  dropdown?: boolean;
  dropdownItems?: {label: string; value: string}[];
  onSelectItem?: (item: {label: string; value: string}) => void;
  brand?: string;
}

// Simplified dropdown items (reduced repetition)
const DEFAULT_DROPDOWN_ITEMS = [
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
];

// Animation dimensions map with optimal sizes
const BUTTON_ANIMATIONS = [
  {width: 594, height: 104, key: 'buttonPill'},
  {width: 520, height: 172, key: 'buttonPill2'},
  {width: 321, height: 54, key: 'buttonPill3'},
  {width: 489, height: 86, key: 'buttonPill4'},
];

/**
 * Gets the most appropriate button animation based on width and height
 */
export const getButtonAnimation = (
  style?: StyleProp<ViewStyle>,
): AnimationObject => {
  if (!style) {
    return animation[BUTTON_ANIMATIONS[0].key as keyof typeof animation];
  }

  const flatStyle = StyleSheet.flatten(style);
  const styleWidth =
    typeof flatStyle.width === 'number'
      ? flatStyle.width
      : typeof flatStyle.maxWidth === 'number'
        ? flatStyle.maxWidth
        : 596;

  const styleHeight =
    typeof flatStyle.height === 'number' ? flatStyle.height : 104;

  // Find the closest matching animation using Euclidean distance
  let bestMatch = BUTTON_ANIMATIONS[0];
  let minDistance = Math.sqrt(
    Math.pow(styleWidth - BUTTON_ANIMATIONS[0].width, 2) +
      Math.pow(styleHeight - BUTTON_ANIMATIONS[0].height, 2),
  );

  for (let i = 1; i < BUTTON_ANIMATIONS.length; i++) {
    const distance = Math.sqrt(
      Math.pow(styleWidth - BUTTON_ANIMATIONS[i].width, 2) +
        Math.pow(styleHeight - BUTTON_ANIMATIONS[i].height, 2),
    );

    if (distance < minDistance) {
      minDistance = distance;
      bestMatch = BUTTON_ANIMATIONS[i];
    }
  }

  return animation[bestMatch.key as keyof typeof animation];
};

// Helper for brand size
const getBrandSize = (brand: string): number => {
  const sizes: Record<string, number> = {
    head: 30,
    bullaPadel: 70,
    nox: 40,
    technifibre: 60,
  };

  return scale(sizes[brand] || 50);
};

const CustomButton: React.FC<CustomButtonProps> = ({
  text,
  text2,
  onPress,
  style,
  textStyle,
  highlighted = false,
  icon,
  buttonContentContainerStyle,
  size,
  dropdown = false,
  dropdownItems = [],
  onSelectItem,
  brand,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef<LottieView>(null);

  const itemsToShow =
    dropdownItems.length > 0 ? dropdownItems : DEFAULT_DROPDOWN_ITEMS;

  const handlePress = () => {
    if (dropdown) {
      setShowDropdown(true);
    } else if (onPress) {
      setIsAnimating(true);
      animationRef.current?.play();
    }
  };

  const handleAnimationFinish = () => {
    if (isAnimating && onPress) {
      setIsAnimating(false);
      onPress();
    }
  };

  const handleSelectItem = (item: {label: string; value: string}) => {
    setShowDropdown(false);
    if (onSelectItem) {
      onSelectItem(item);
    }
  };

  const buttonAnimation = getButtonAnimation(style);
  const flatStyle = style ? StyleSheet.flatten(style) : {};
  const {width: styleWidth} = flatStyle;

  return (
    <View style={{position: 'relative'}}>
      <TouchableHighlight
        style={[
          styles.button,
          highlighted ? styles.highlightedButton : {},
          style,
        ]}
        onPress={handlePress}
        activeOpacity={1}
        underlayColor="#DFFC4F">
        <View
          style={[
            styles.buttonContainer,
            {
              alignItems: icon ? 'flex-start' : 'center',
            },
          ]}>
          <LottieView
            loop={false}
            ref={animationRef}
            source={buttonAnimation}
            speed={1.25}
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              opacity: isAnimating ? 1 : 0,
            }}
            onAnimationFinish={handleAnimationFinish}
          />
          <View
            style={[
              styles.buttonContent,
              dropdown ? styles.dropdownButtonContent : {},
              {
                paddingLeft: icon ? scale(20) : scale(0),
              },
              buttonContentContainerStyle,
            ]}>
            {icon && (
              <View style={styles.iconContainer}>
                <Icon
                  name={icon}
                  color={KioskConfig.theme.colors.text.primary}
                  size={size || scale(50)}
                />
              </View>
            )}
            {brand ? (
              <View style={{width: '100%', alignItems: 'center'}}>
                <Icon
                  name={getBrandName(brand)}
                  size={getBrandSize(brand)}
                  color="black"
                />
              </View>
            ) : (
              <View style={{flexDirection: 'column'}}>
                <Typography
                  variant="customButton"
                  style={
                    textStyle
                      ? [styles.buttonText, textStyle as TextStyle]
                      : styles.buttonText
                  }>
                  {text}
                </Typography>
                {text2 && (
                  <Typography
                    variant="customButton"
                    style={
                      textStyle
                        ? [styles.buttonText, textStyle as TextStyle]
                        : styles.buttonText
                    }>
                    {text2}
                  </Typography>
                )}
              </View>
            )}
            {dropdown && (
              <Icon
                name="dropdown"
                color={KioskConfig.theme.colors.text.primary}
                size={scale(40)}
                style={styles.dropdownIcon}
              />
            )}
          </View>
        </View>
      </TouchableHighlight>

      {dropdown && (
        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}>
          <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
            <View style={styles.modalOverlay}>
              <View
                style={[
                  styles.dropdownContainer,
                  {top: verticalScale(110)},
                  styleWidth !== undefined ? {width: styleWidth} : {},
                ]}>
                <ScrollView>
                  {itemsToShow.map((item, index) => (
                    <TouchableHighlight
                      key={index}
                      style={[styles.button, styles.dropdownItem]}
                      onPress={() => handleSelectItem(item)}
                      activeOpacity={1}
                      underlayColor="#DFFC4F">
                      <Typography
                        variant="customButton"
                        style={
                          textStyle
                            ? [styles.buttonText, textStyle as TextStyle]
                            : styles.buttonText
                        }>
                        {item.label}
                      </Typography>
                    </TouchableHighlight>
                  ))}
                </ScrollView>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
    justifyContent: 'center',
  },
  button: {
    backgroundColor: KioskConfig.theme.colors.white,
    borderRadius: scale(124),
    maxWidth: scale(596),
    height: verticalScale(104),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: scale(2),
    borderColor: '#DFFC4F',
  },
  highlightedButton: {
    backgroundColor: '#DFFC4F',
  },
  buttonText: {
    color: '#262626',
    textAlign: 'center',
  },
  buttonContent: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  dropdownButtonContent: {
    position: 'relative',
    width: '100%',
    paddingHorizontal: scale(20),
  },
  iconContainer: {
    minWidth: scale(80),
    minHeight: scale(80),
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownIcon: {
    position: 'absolute',
    right: scale(20),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownContainer: {
    position: 'absolute',
    maxHeight: verticalScale(900),
    width: scale(596),
    backgroundColor: 'white',
    borderRadius: scale(20),
    borderWidth: scale(2),
    borderColor: '#DFFC4F',
    overflow: 'hidden',
  },
  dropdownItem: {
    borderRadius: 0,
    borderWidth: 0,
    borderBottomWidth: scale(1),
    borderColor: '#EFEFEF',
  },
});

export default CustomButton;
