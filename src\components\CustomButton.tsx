/**
 * @fileoverview CustomButton component for the Kiosk application
 * @module Components/CustomButton
 * @category Components
 */

import React, {useState, useRef} from 'react';
import {
  TouchableHighlight,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  View,
  Modal,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import Typography from './Typography';
import Icon from './Icon';
import KioskConfig from '@/config/KioskConfig';
import {scale, verticalScale} from '@/utils/scale';
import animation from '@/config/animation';
import LottieView, {AnimationObject} from 'lottie-react-native';
import {getBrandName} from '@/utils/CommonFunctions';

/**
 * @typedef {Object} DropdownItem
 * @property {string} label - The display text for the dropdown item
 * @property {string} value - The value associated with the dropdown item
 */

/**
 * Props interface for the CustomButton component
 * @interface CustomButtonProps
 * @property {string} text - Primary text to display on the button
 * @property {string} [text2] - Optional secondary text to display below primary text
 * @property {Function} [onPress] - Callback function executed when button is pressed
 * @property {StyleProp<ViewStyle>} [style] - Custom styles for the button container
 * @property {StyleProp<TextStyle>} [textStyle] - Custom styles for the button text
 * @property {boolean} [highlighted=false] - Whether the button should appear highlighted
 * @property {string} [icon] - Icon name to display on the button
 * @property {StyleProp<ViewStyle>} [buttonContentContainerStyle] - Custom styles for button content container
 * @property {number} [size] - Size of the icon (if icon is provided)
 * @property {boolean} [dropdown=false] - Whether the button should show a dropdown menu
 * @property {DropdownItem[]} [dropdownItems] - Array of items to display in dropdown
 * @property {Function} [onSelectItem] - Callback function when dropdown item is selected
 * @property {string} [brand] - Brand name to display as icon instead of text
 */
interface CustomButtonProps {
  text: string;
  text2?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  highlighted?: boolean;
  icon?: string;
  buttonContentContainerStyle?: StyleProp<ViewStyle>;
  size?: number;
  dropdown?: boolean;
  dropdownItems?: {label: string; value: string}[];
  onSelectItem?: (item: {label: string; value: string}) => void;
  brand?: string;
}

/**
 * Default dropdown items used when no custom items are provided
 * @constant {DropdownItem[]}
 */
const DEFAULT_DROPDOWN_ITEMS = [
  {label: 'Option 1', value: 'option1'},
  {label: 'Option 2', value: 'option2'},
  {label: 'Option 3', value: 'option3'},
];

/**
 * Animation dimensions map with optimal sizes for different button configurations
 * @constant {Object[]}
 * @property {number} width - Animation width in pixels
 * @property {number} height - Animation height in pixels
 * @property {string} key - Animation key reference
 */
const BUTTON_ANIMATIONS = [
  {width: 594, height: 104, key: 'buttonPill'},
  {width: 520, height: 172, key: 'buttonPill2'},
  {width: 321, height: 54, key: 'buttonPill3'},
  {width: 489, height: 86, key: 'buttonPill4'},
];

/**
 * Gets the most appropriate button animation based on width and height using Euclidean distance
 * @function getButtonAnimation
 * @param {StyleProp<ViewStyle>} [style] - Button style containing width and height
 * @returns {AnimationObject} The best matching Lottie animation object
 * @example
 * const animation = getButtonAnimation({width: 500, height: 100});
 */
export const getButtonAnimation = (
  style?: StyleProp<ViewStyle>,
): AnimationObject => {
  if (!style) {
    return animation[BUTTON_ANIMATIONS[0].key as keyof typeof animation];
  }

  const flatStyle = StyleSheet.flatten(style);
  const styleWidth =
    typeof flatStyle.width === 'number'
      ? flatStyle.width
      : typeof flatStyle.maxWidth === 'number'
        ? flatStyle.maxWidth
        : 596;

  const styleHeight =
    typeof flatStyle.height === 'number' ? flatStyle.height : 104;

  // Find the closest matching animation using Euclidean distance
  let bestMatch = BUTTON_ANIMATIONS[0];
  let minDistance = Math.sqrt(
    Math.pow(styleWidth - BUTTON_ANIMATIONS[0].width, 2) +
      Math.pow(styleHeight - BUTTON_ANIMATIONS[0].height, 2),
  );

  for (let i = 1; i < BUTTON_ANIMATIONS.length; i++) {
    const distance = Math.sqrt(
      Math.pow(styleWidth - BUTTON_ANIMATIONS[i].width, 2) +
        Math.pow(styleHeight - BUTTON_ANIMATIONS[i].height, 2),
    );

    if (distance < minDistance) {
      minDistance = distance;
      bestMatch = BUTTON_ANIMATIONS[i];
    }
  }

  return animation[bestMatch.key as keyof typeof animation];
};

/**
 * Helper function to get the appropriate size for brand icons
 * @function getBrandSize
 * @param {string} brand - Brand name identifier
 * @returns {number} Scaled size value for the brand icon
 * @example
 * const size = getBrandSize('head'); // returns scaled 30
 */
const getBrandSize = (brand: string): number => {
  const sizes: Record<string, number> = {
    head: 30,
    bullaPadel: 70,
    nox: 40,
    technifibre: 60,
  };

  return scale(sizes[brand] || 50);
};

/**
 * CustomButton - A versatile button component with animation, dropdown, and brand support
 *
 * This component provides a highly customizable button with the following features:
 * - Lottie animations on press
 * - Dropdown functionality with modal overlay
 * - Brand icon display
 * - Icon support with customizable sizes
 * - Highlighted state styling
 * - Responsive design with scaling utilities
 *
 * @component
 * @param {CustomButtonProps} props - The props for the CustomButton component
 * @returns {JSX.Element} The rendered CustomButton component
 *
 * @example
 * // Basic button
 * <CustomButton text="Click Me" onPress={() => console.log('Pressed')} />
 *
 * @example
 * // Button with icon
 * <CustomButton
 *   text="Settings"
 *   icon="settings"
 *   size={40}
 *   onPress={handleSettings}
 * />
 *
 * @example
 * // Dropdown button
 * <CustomButton
 *   text="Select Option"
 *   dropdown={true}
 *   dropdownItems={[
 *     {label: 'Option 1', value: 'opt1'},
 *     {label: 'Option 2', value: 'opt2'}
 *   ]}
 *   onSelectItem={(item) => console.log(item.value)}
 * />
 *
 * @example
 * // Brand button
 * <CustomButton brand="head" />
 */
const CustomButton: React.FC<CustomButtonProps> = ({
  text,
  text2,
  onPress,
  style,
  textStyle,
  highlighted = false,
  icon,
  buttonContentContainerStyle,
  size,
  dropdown = false,
  dropdownItems = [],
  onSelectItem,
  brand,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef<LottieView>(null);

  const itemsToShow =
    dropdownItems.length > 0 ? dropdownItems : DEFAULT_DROPDOWN_ITEMS;

  const handlePress = () => {
    if (dropdown) {
      setShowDropdown(true);
    } else if (onPress) {
      setIsAnimating(true);
      animationRef.current?.play();
    }
  };

  const handleAnimationFinish = () => {
    if (isAnimating && onPress) {
      setIsAnimating(false);
      onPress();
    }
  };

  const handleSelectItem = (item: {label: string; value: string}) => {
    setShowDropdown(false);
    if (onSelectItem) {
      onSelectItem(item);
    }
  };

  const buttonAnimation = getButtonAnimation(style);
  const flatStyle = style ? StyleSheet.flatten(style) : {};
  const {width: styleWidth} = flatStyle;

  return (
    <View style={{position: 'relative'}}>
      <TouchableHighlight
        style={[
          styles.button,
          highlighted ? styles.highlightedButton : {},
          style,
        ]}
        onPress={handlePress}
        activeOpacity={1}
        underlayColor="#DFFC4F">
        <View
          style={[
            styles.buttonContainer,
            {
              alignItems: icon ? 'flex-start' : 'center',
            },
          ]}>
          <LottieView
            loop={false}
            ref={animationRef}
            source={buttonAnimation}
            speed={1.25}
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
              opacity: isAnimating ? 1 : 0,
            }}
            onAnimationFinish={handleAnimationFinish}
          />
          <View
            style={[
              styles.buttonContent,
              dropdown ? styles.dropdownButtonContent : {},
              {
                paddingLeft: icon ? scale(20) : scale(0),
              },
              buttonContentContainerStyle,
            ]}>
            {icon && (
              <View style={styles.iconContainer}>
                <Icon
                  name={icon}
                  color={KioskConfig.theme.colors.text.primary}
                  size={size || scale(50)}
                />
              </View>
            )}
            {brand ? (
              <View style={{width: '100%', alignItems: 'center'}}>
                <Icon
                  name={getBrandName(brand)}
                  size={getBrandSize(brand)}
                  color="black"
                />
              </View>
            ) : (
              <View style={{flexDirection: 'column'}}>
                <Typography
                  variant="customButton"
                  style={
                    textStyle
                      ? [styles.buttonText, textStyle as TextStyle]
                      : styles.buttonText
                  }>
                  {text}
                </Typography>
                {text2 && (
                  <Typography
                    variant="customButton"
                    style={
                      textStyle
                        ? [styles.buttonText, textStyle as TextStyle]
                        : styles.buttonText
                    }>
                    {text2}
                  </Typography>
                )}
              </View>
            )}
            {dropdown && (
              <Icon
                name="dropdown"
                color={KioskConfig.theme.colors.text.primary}
                size={scale(40)}
                style={styles.dropdownIcon}
              />
            )}
          </View>
        </View>
      </TouchableHighlight>

      {dropdown && (
        <Modal
          visible={showDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDropdown(false)}>
          <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
            <View style={styles.modalOverlay}>
              <View
                style={[
                  styles.dropdownContainer,
                  {top: verticalScale(110)},
                  styleWidth !== undefined ? {width: styleWidth} : {},
                ]}>
                <ScrollView>
                  {itemsToShow.map((item, index) => (
                    <TouchableHighlight
                      key={index}
                      style={[styles.button, styles.dropdownItem]}
                      onPress={() => handleSelectItem(item)}
                      activeOpacity={1}
                      underlayColor="#DFFC4F">
                      <Typography
                        variant="customButton"
                        style={
                          textStyle
                            ? [styles.buttonText, textStyle as TextStyle]
                            : styles.buttonText
                        }>
                        {item.label}
                      </Typography>
                    </TouchableHighlight>
                  ))}
                </ScrollView>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
    justifyContent: 'center',
  },
  button: {
    backgroundColor: KioskConfig.theme.colors.white,
    borderRadius: scale(124),
    maxWidth: scale(596),
    height: verticalScale(104),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: scale(2),
    borderColor: '#DFFC4F',
  },
  highlightedButton: {
    backgroundColor: '#DFFC4F',
  },
  buttonText: {
    color: '#262626',
    textAlign: 'center',
  },
  buttonContent: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  dropdownButtonContent: {
    position: 'relative',
    width: '100%',
    paddingHorizontal: scale(20),
  },
  iconContainer: {
    minWidth: scale(80),
    minHeight: scale(80),
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownIcon: {
    position: 'absolute',
    right: scale(20),
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdownContainer: {
    position: 'absolute',
    maxHeight: verticalScale(900),
    width: scale(596),
    backgroundColor: 'white',
    borderRadius: scale(20),
    borderWidth: scale(2),
    borderColor: '#DFFC4F',
    overflow: 'hidden',
  },
  dropdownItem: {
    borderRadius: 0,
    borderWidth: 0,
    borderBottomWidth: scale(1),
    borderColor: '#EFEFEF',
  },
});

export default CustomButton;
