import React, {useState, useEffect} from 'react';
import {
  Image,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Typography from '../Typography';
import KioskConfig from '@/config/KioskConfig';
import GripRadio from './GripRadio';
import AnimatedButton from '../AnimatedButton';
import images from '@/config/images';
import RadioSelect from '../CRadioSelect';
import Counter from '../Counter';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import Icon from '../Icon';
import {getBrandName} from '@/utils/CommonFunctions';
import {scale, verticalScale} from '@/utils/scale';

interface GripSize {
  value: string;
  label: string;
}

interface Color {
  value: string;
  label: string;
}

interface AnimatedProductDetailsProps {
  title: string;
  description: string;
  containerStyle?: ViewStyle;
  btnContainerStyle?: ViewStyle;
  btnStyle?: ViewStyle;
  textStyle?: TextStyle;
  actionButtons: ActionButton[];
  gripSizes?: GripSize[];
  colors?: Color[];
  radioOptions?: RadioOption[];
  radioTitle?: string;
  showCounter?: boolean;
  counterTitle?: string;
  calendar?: () => React.ReactNode;
  brand?: string;
}

interface RadioOption {
  label: string;
  value: string;
}

interface ActionButton {
  id: number;
  title: string;
  onPress: () => void;
  icon?: string;
  highlighted?: boolean;
}

const AnimatedProductDetails = (props: AnimatedProductDetailsProps) => {
  const {
    title,
    description,
    containerStyle,
    btnContainerStyle,
    btnStyle,
    textStyle,
    actionButtons,
    gripSizes = [],
    colors = [],
    radioOptions = [],
    radioTitle = '',
    showCounter = false,
    counterTitle = '',
    calendar,
    brand,
  } = props;

  const [radioSelected, setRadioSelected] = useState<string>('');
  const [counterValue, setCounterValue] = useState<number>(0);

  // Single animation value for the entire component
  const opacity = useSharedValue(0);

  // Initialize animation on mount
  useEffect(() => {
    const animationConfig = {duration: 500, easing: Easing.out(Easing.ease)};

    // Simple fade-in animation for the entire component
    opacity.value = withTiming(1, animationConfig);
  }, []);

  // Animated style for the entire component
  const fadeStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  // Simple handler for radio selection
  const handleRadioSelect = (value: string) => {
    setRadioSelected(value);
  };

  return (
    <Animated.View style={[styles.container, containerStyle, fadeStyle]}>
      <Icon
        name={getBrandName(brand)}
        size={brand === 'head' ? scale(30) : scale(41)}
        color="white"
      />
      <View style={styles.contentContainer}>
        <Typography
          variant="dunlopTitle"
          color={KioskConfig.theme.colors.white}>
          {title}
        </Typography>

        <Typography
          variant="dunlopDescription"
          style={styles.description}
          color={KioskConfig.theme.colors.white}>
          {description}
        </Typography>
      </View>

      {radioOptions?.length > 0 && (
        <View>
          <Typography
            variant="RadioTitle"
            color={KioskConfig.theme.colors.white}
            style={styles.colorTitle}>
            {radioTitle}
          </Typography>
          <View style={styles.radioContainer}>
            {radioOptions?.map(option => (
              <RadioSelect
                key={option.value}
                label={option.label}
                selected={radioSelected === option.value}
                onPress={() => {
                  handleRadioSelect(option.value);
                }}
              />
            ))}
          </View>
        </View>
      )}

      {showCounter && (
        <View>
          <Typography
            variant="counterLabel"
            color={KioskConfig.theme.colors.white}
            style={styles.colorTitle}>
            {counterTitle}
          </Typography>
          <View style={styles.counterContainer}>
            <Counter
              value={counterValue}
              onDecrement={() => setCounterValue(prev => Math.max(0, prev - 1))}
              onIncrement={() => setCounterValue(prev => prev + 1)}
            />
          </View>
        </View>
      )}

      {calendar ? (
        <View style={[styles.buttonContainer]}>{calendar()} </View>
      ) : null}

      <View style={[styles.buttonContainer, btnContainerStyle]}>
        {actionButtons.map(data => (
          <AnimatedButton
            key={data.id}
            text={data.title}
            onPress={data.onPress}
            style={btnStyle}
            icon={data.icon}
            buttonContentContainerStyle={styles.buttonContentContainer}
            textStyle={textStyle}
          />
        ))}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  contentContainer: {
    alignItems: 'flex-start',
    marginTop: verticalScale(20),
  },
  description: {
    marginTop: scale(20),
  },
  gripRadioContainer: {
    marginTop: scale(10),
    flexDirection: 'row',
    gap: scale(20),
  },
  colorContainer: {
    marginTop: scale(10),
    flexDirection: 'row',
    gap: scale(20),
  },
  colorView: {
    height: verticalScale(51.94),
    width: scale(51.94),
    borderRadius: scale(200),
  },
  colorTitle: {
    marginTop: scale(30),
  },
  buttonContainer: {
    display: 'flex',
    gap: scale(20),
    marginTop: scale(100),
  },
  buttonContentContainer: {
    gap: scale(25),
  },
  helpMeChooseContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: scale(10),
    flexDirection: 'row',
    gap: scale(10),
  },
  helloImage: {
    width: scale(35),
    height: verticalScale(35),
  },
  helpMeChooseText: {
    marginTop: scale(10),
  },
  gripTitle: {
    marginTop: scale(20),
  },
  radioContainer: {
    marginTop: scale(10),
    flexDirection: 'column',
    gap: scale(5),
  },
  counterContainer: {
    marginTop: scale(10),
  },
});

export default AnimatedProductDetails;
