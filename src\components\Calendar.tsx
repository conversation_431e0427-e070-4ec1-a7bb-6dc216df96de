/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/no-unstable-nested-components */
import {FONTS} from '@/utils/fonts';
import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {Calendar as RNCalendar} from 'react-native-calendars';
import {MarkedDates, Theme} from 'react-native-calendars/src/types';

// Get screen dimensions for dynamic sizing
const {width: screenWidth} = Dimensions.get('window');

interface CalendarProps {
  selectedDate: Date;
  onSelectDate: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
}

interface CustomHeaderProps {
  date: Date | string;
  addMonth: () => void;
  subtractMonth: () => void;
}

// Custom Header Component
const CustomHeader = ({date, addMonth, subtractMonth}: CustomHeaderProps) => {
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const headerDate = new Date(date);
  const month = monthNames[headerDate.getMonth()];
  const year = headerDate.getFullYear();

  return (
    <View style={styles.customHeaderContainer}>
      <Text style={styles.headerTitle}>
        {month} {year}
      </Text>
      <View style={styles.headerArrows}>
        <TouchableOpacity onPress={subtractMonth}>
          <Text style={styles.headerArrow}>{'<'}</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={addMonth}>
          <Text style={styles.headerArrow}>{'>'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Custom Day Names Row
const CustomDayNames = () => {
  return (
    <View style={styles.dayNamesContainer}>
      <Text style={styles.dayName}>SUN</Text>
      <Text style={styles.dayName}>MON</Text>
      <Text style={styles.dayName}>TUE</Text>
      <Text style={styles.dayName}>WED</Text>
      <Text style={styles.dayName}>THU</Text>
      <Text style={styles.dayName}>FRI</Text>
      <Text style={styles.dayName}>SAT</Text>
    </View>
  );
};

const Calendar: React.FC<CalendarProps> = ({
  selectedDate,
  onSelectDate,
  maxDate,
}) => {
  const [startTime, setStartTime] = useState('8:00');
  const [endTime, setEndTime] = useState('8:00');
  const [startAmPm, setStartAmPm] = useState('AM');
  const [endAmPm, setEndAmPm] = useState('PM');
  const [currentMonth, setCurrentMonth] = useState(selectedDate);

  // Format date for the calendar component
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Get today's date formatted
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Remove time portion
  const todayFormatted = formatDate(today);

  // Generate the marked dates object
  const getMarkedDates = (): MarkedDates => {
    const formattedDate = formatDate(selectedDate);
    return {
      [formattedDate]: {
        selected: true,
        selectedColor: '#007AFF',
      },
    };
  };

  // Handle time changes
  const changeTime = (timeType: 'start' | 'end', increment: boolean) => {
    const timeString = timeType === 'start' ? startTime : endTime;
    const [hours, minutes] = timeString.split(':').map(Number);

    let newHours = hours;
    if (increment) {
      newHours = hours >= 23 ? 0 : hours + 1;
    } else {
      newHours = hours <= 0 ? 23 : hours - 1;
    }

    const newTime = `${String(newHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

    if (timeType === 'start') {
      setStartTime(newTime);
    } else {
      setEndTime(newTime);
    }
  };

  // Check if a date is before today
  const isBeforeToday = (date: string): boolean => {
    const dateObj = new Date(date);
    dateObj.setHours(0, 0, 0, 0);
    return dateObj < today;
  };

  // Custom theme for calendar
  const calendarTheme: Theme = {
    backgroundColor: '#DDDDDD',
    calendarBackground: '#DDDDDD',
    textSectionTitleColor: '#AAAAAA',
    selectedDayBackgroundColor: '#007AFF',
    selectedDayTextColor: '#ffffff',
    textMonthFontFamily: FONTS.regular,
    todayTextColor: '#007AFF',
    textDisabledColor: '#ccc',
    arrowColor: '#007AFF',
    monthTextColor: '#333',
    dayTextColor: '#2d4150',
    textDayFontWeight: '400',
    textMonthFontWeight: '500',
    textDayHeaderFontWeight: '400',
    textDayFontSize: 16,
    textDayFontFamily: FONTS.regular,
  };

  return (
    <View style={styles.container}>
      <View style={styles.calendarContainer}>
        {/* Custom Header */}
        <CustomHeader
          date={currentMonth}
          addMonth={() => {
            const newDate = new Date(currentMonth);
            newDate.setMonth(newDate.getMonth() + 1);
            setCurrentMonth(newDate);
          }}
          subtractMonth={() => {
            const newDate = new Date(currentMonth);
            newDate.setMonth(newDate.getMonth() - 1);
            setCurrentMonth(newDate);
          }}
        />

        {/* Day Names */}
        <CustomDayNames />

        <RNCalendar
          current={formatDate(currentMonth)}
          minDate={todayFormatted} // Use today as minimum date to disable past dates
          maxDate={maxDate ? formatDate(maxDate) : undefined}
          onDayPress={day => {
            const newDate = new Date(day.timestamp);
            onSelectDate(newDate);
          }}
          onMonthChange={month => {
            setCurrentMonth(new Date(month.timestamp));
          }}
          markedDates={getMarkedDates()}
          hideExtraDays={false}
          enableSwipeMonths={true}
          theme={calendarTheme}
          style={styles.calendar}
          hideArrows={true}
          renderHeader={() => null}
          hideDayNames={true} // Hide default day names
          // Custom day component to create grid with no spacing
          dayComponent={({date, state, marking}) => {
            const isSelected = marking && marking.selected;
            const isDisabled =
              state === 'disabled' || (date && isBeforeToday(date.dateString));

            return (
              <View style={styles.dayContainer}>
                <View
                  style={[
                    styles.dayInner,
                    isSelected && {
                      backgroundColor: '#007AFF',
                      width: 50,
                      height: 50,
                      borderRadius: 50,
                    },
                  ]}>
                  <Text
                    style={[
                      {fontSize: 20},
                      {color: isDisabled ? '#999' : '#333'},
                      isSelected && {color: '#ffffff'},
                    ]}>
                    {date ? date.day : ''}
                  </Text>
                </View>
              </View>
            );
          }}
        />
      </View>

      <View style={styles.calendarFooter}>
        <View style={styles.footerRow}>
          <Text style={styles.footerLabel}>Starts</Text>
          <View style={styles.footerTime}>
            <Text style={styles.timeText}>{startTime}</Text>
            <View style={styles.timeButtons}>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => changeTime('start', true)}>
                <Text style={styles.timeButtonText}>+</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => changeTime('start', false)}>
                <Text style={styles.timeButtonText}>−</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.amPmContainer}>
              <TouchableOpacity
                style={[
                  styles.amPmButton,
                  startAmPm === 'AM' && styles.amPmButtonActive,
                ]}
                onPress={() => setStartAmPm('AM')}>
                <Text
                  style={[
                    styles.amPmText,
                    startAmPm === 'AM' && styles.amPmTextActive,
                  ]}>
                  AM
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.amPmButton,
                  startAmPm === 'PM' && styles.amPmButtonActive,
                ]}
                onPress={() => setStartAmPm('PM')}>
                <Text
                  style={[
                    styles.amPmText,
                    startAmPm === 'PM' && styles.amPmTextActive,
                  ]}>
                  PM
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.footerRow}>
          <Text style={styles.footerLabel}>Ends</Text>
          <View style={styles.footerTime}>
            <Text style={styles.timeText}>{endTime}</Text>
            <View style={styles.timeButtons}>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => changeTime('end', true)}>
                <Text style={styles.timeButtonText}>+</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => changeTime('end', false)}>
                <Text style={styles.timeButtonText}>−</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.amPmContainer}>
              <TouchableOpacity
                style={[
                  styles.amPmButton,
                  endAmPm === 'AM' && styles.amPmButtonActive,
                ]}
                onPress={() => setEndAmPm('AM')}>
                <Text
                  style={[
                    styles.amPmText,
                    endAmPm === 'AM' && styles.amPmTextActive,
                  ]}>
                  AM
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.amPmButton,
                  endAmPm === 'PM' && styles.amPmButtonActive,
                ]}
                onPress={() => setEndAmPm('PM')}>
                <Text
                  style={[
                    styles.amPmText,
                    endAmPm === 'PM' && styles.amPmTextActive,
                  ]}>
                  PM
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#DDDDDD',
    borderRadius: 10,
    overflow: 'hidden',
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    width: screenWidth / 4,
  },
  calendarContainer: {
    padding: 0,
  },
  calendar: {
    backgroundColor: '#DDDDDD',
    borderRadius: 0,
  },
  dayContainer: {
    width: '100%', // Full width to create grid
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 0,
    margin: 0,
    borderWidth: 0,
  },
  dayInner: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarFooter: {
    paddingHorizontal: 15,
    paddingVertical: 15,
    backgroundColor: '#DDDDDD',
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  footerLabel: {
    fontSize: 18,
    color: '#666',
    fontWeight: '500',
  },
  footerTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 18,
    color: '#333',
    marginRight: 10,
    width: 40,
    textAlign: 'center',
    fontWeight: '500',
  },
  timeButtons: {
    flexDirection: 'row',
  },
  timeButton: {
    width: 32,
    height: 32,
    borderWidth: 0.5,
    borderColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 3,
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  timeButtonText: {
    fontSize: 18,
    color: '#333',
    fontWeight: 'bold',
  },
  customHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 10,
    backgroundColor: '#DDDDDD',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
    fontFamily: FONTS.regular,
  },
  headerArrows: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerArrow: {
    fontSize: 24,
    color: '#007AFF',
    marginHorizontal: 10,
  },
  dayNamesContainer: {
    flexDirection: 'row',
    backgroundColor: '#DDDDDD',
    paddingVertical: 5,
    borderBottomWidth: 0,
  },
  dayName: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    color: '#999',
    fontWeight: '500',
    fontFamily: FONTS.regular,
  },
  amPmContainer: {
    flexDirection: 'row',
    marginLeft: 5,
  },
  amPmButton: {
    width: 40,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#EEEEEE',
    borderRadius: 4,
    marginHorizontal: 2,
  },
  amPmButtonActive: {
    backgroundColor: '#666',
  },
  amPmText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  amPmTextActive: {
    color: 'white',
  },
});

export default Calendar;
