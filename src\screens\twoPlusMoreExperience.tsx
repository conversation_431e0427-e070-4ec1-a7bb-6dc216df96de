import GradientBackground from '@/components/GradientBackground';
import SelectorFlow from '@/components/SelectorFlow';
import {MainStackParamList} from '@/navigation';
import {RouteProp} from '@react-navigation/native';
import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {useLanguageStore} from '@/store/languageStore';
export type TwoPlusMoreExperienceProps = {
  route: RouteProp<MainStackParamList, 'twoPlusMoreExperience'>;
};

const TwoPlusMoreExperience = ({route}: TwoPlusMoreExperienceProps) => {
  const {from} = route.params || {};
  const navigation = useNavigation<StackNavigationProp<MainStackParamList>>();
  const {t} = useLanguageStore();
  return (
    <GradientBackground>
      <SelectorFlow
        from={from}
        description={t('TwoPlusMoreExperience.description')}
        btnText1={{
          text: t('TwoPlusMoreExperience.btnText1'),
          text2: t('TwoPlusMoreExperience.btnText2'),
        }}
        btnText2={{
          text: t('TwoPlusMoreExperience.btnText3'),
          text2: t('TwoPlusMoreExperience.btnText4'),
        }}
        onPress={() => {
          navigation.navigate('workOnFinerPoints', {
            from: from,
          });
        }}
        onPress2={() => {
          navigation.navigate('playCompititily', {
            from: from,
          });
        }}
      />
    </GradientBackground>
  );
};

export default TwoPlusMoreExperience;
